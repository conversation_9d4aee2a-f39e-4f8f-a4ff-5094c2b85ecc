# Meta Media Download API (NestJS)

## Overview
This NestJS API endpoint allows you to download media files from Meta's WhatsApp Business API using the shop's stored access token. The API automatically retrieves the access token from the database based on the shop ID.

## Endpoint
```
POST /commerce/wa-business-management/download-media/{shopId}
```

## Authentication
- Requires JWT Bearer token in Authorization header
- Uses `@ApiBearerAuth('access-token')` decorator
- Restricted to 'merchant' role

## Parameters

### Path Parameters
- `shopId` (string, required): The ID of the shop

### Request Body (DownloadMediaDto)
```json
{
  "mediaId": "string",     // Required: Meta media ID to download
  "download": boolean      // Optional: Whether to force download as attachment (default: false)
}
```

## Response
- **Success (200)**: Returns the media file with appropriate headers
- **Bad Request (400)**: Invalid request or missing parameters
- **Not Found (404)**: Media not found

## Headers Set in Response
- `Content-Type`: The MIME type of the media file
- `Content-Length`: Size of the file in bytes
- `Cache-Control`: `public, max-age=3600` (1 hour cache)
- `Content-Disposition`: Set to `attachment` if `download=true`

## Supported Media Types
- Images: JPEG, PNG, GIF, WebP
- Audio: MP3, M4A, OGG, WAV
- Video: MP4, MOV, WebM
- Documents: PDF, DOC, DOCX, TXT

## Example Usage

### JavaScript/Fetch (Frontend)
```javascript
const response = await fetch('/commerce/wa-business-management/download-media/shop123', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer your-jwt-token'
  },
  body: JSON.stringify({
    mediaId: 'media123456',
    download: true
  })
});

if (response.ok) {
  const blob = await response.blob();
  const url = window.URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = 'downloaded-media';
  a.click();
} else {
  const error = await response.json();
  console.error('Download failed:', error.message);
}
```

### NestJS Service Usage (Backend)
```typescript
// In another service
constructor(
  private readonly whatsappService: CommerceWhatsappService
) {}

async downloadMedia(shopId: string, mediaId: string): Promise<StreamableFile> {
  const { shopAccessToken } = await this.whatsappService.getBusinessAccountInfo(shopId);

  if (!shopAccessToken) {
    throw new BadRequestException('Shop access token not found');
  }

  const mediaData = await this.whatsappService.downloadMetaMedia(mediaId, shopAccessToken);

  return new StreamableFile(mediaData.buffer, {
    type: mediaData.mimeType,
    disposition: `attachment; filename="${mediaData.fileName}"`,
  });
}
```

### NestJS Controller Pattern
```typescript
@Post('/download-media/:shopId')
@Header('Cache-Control', 'public, max-age=3600')
@ApiBody({ type: DownloadMediaDto })
async downloadMetaMedia(
  @Param('shopId') shopId: string,
  @Body() downloadMediaDto: DownloadMediaDto,
): Promise<StreamableFile> {
  // Implementation returns StreamableFile
}
```

### cURL
```bash
curl -X POST \
  'http://localhost:3000/commerce/wa-business-management/download-media/shop123' \
  -H 'Content-Type: application/json' \
  -H 'Authorization: Bearer your-jwt-token' \
  -d '{
    "mediaId": "media123456",
    "download": true
  }' \
  --output downloaded-media.jpg
```

## How It Works (Pure NestJS)
1. The API receives the shopId and mediaId via DTO validation
2. It retrieves the shop's WhatsApp access token from the database using `getBusinessAccountInfo`
3. It calls Meta's Graph API to get the media URL
4. It downloads the actual media file from Meta's servers
5. It returns the file as a `StreamableFile` with proper NestJS headers (no Express dependency)

## NestJS Implementation Details
- Uses `StreamableFile` for efficient file streaming
- Uses `@Header()` decorator for cache control
- Uses DTO validation with class-validator
- No Express imports - pure NestJS approach
- Automatic content-type detection and disposition headers

## Error Handling
- Returns 400 if mediaId is missing
- Returns 400 if shop access token is not found
- Returns 400 if Meta API calls fail
- Returns 500 for internal server errors

## Security
- Requires JWT authentication
- Uses shop-specific access tokens stored securely in the database
- No direct access token exposure in the API
