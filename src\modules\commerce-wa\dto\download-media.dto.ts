import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsBoolean, IsOptional } from 'class-validator';

export class DownloadMediaDto {
  @ApiProperty({
    description: 'Meta media ID to download',
    example: 'media123456789',
  })
  @IsString()
  mediaId: string;

  @ApiProperty({
    description: 'Whether to force download as attachment',
    default: false,
    required: false,
  })
  @IsBoolean()
  @IsOptional()
  download?: boolean = false;
}
