import {
  BadRequestException,
  Body,
  Controller,
  Delete,
  Get,
  Header,
  HttpStatus,
  Param,
  Patch,
  Post,
  Put,
  Query,
  StreamableFile,
  UploadedFile,
  UseInterceptors,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import {
  ApiBearerAuth,
  ApiBody,
  ApiConsumes,
  ApiOperation,
  ApiParam,
  ApiQuery,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { Roles } from '../../common/decorators/roles.decorator';
import { CommerceWhatsappService } from './whatsapp.service';
import { Public } from 'src/common/decorators/public.decorator';
import { DownloadMediaDto } from './dto/download-media.dto';

export interface PresignedPostFields {
  [key: string]: string | number;
  key: string;
  'Content-Type': string;
}

@ApiBearerAuth('access-token')
@Roles('merchant')
@ApiTags('Whatsapp Broadcast')
@Controller('commerce/wa-business-management')
export class CommerceWhatsappController {
  constructor(private readonly whatsappService: CommerceWhatsappService) {}

  @Get('/templates/:shopId')
  @ApiParam({ name: 'shopId', required: true, type: String })
  @ApiQuery({ name: 'branchId', required: false, type: String })
  @ApiOperation({ summary: 'Get all Templates from META' })
  @ApiResponse({ status: HttpStatus.OK, description: 'The list of customers.' })
  async getTemplates(
    @Param('shopId') shopId: string,
    @Query('branchId') branchId?: string,
  ) {
    const { businessAccountId, shopAccessToken } =
      await this.whatsappService.getBusinessAccountInfo(shopId);
    try {
      if (businessAccountId && shopAccessToken) {
        const templateData = await this.whatsappService.getTemplates(
          {
            businessAccountId,
            shopAccessToken,
          },
          shopId,
            this.whatsappService.normalizeToNull(branchId) || '',
        );
        return templateData;
      } else {
        return false;
      }
    } catch (error) {
      throw new BadRequestException(error?.response);
    }
  }

  @Post('/templates/:shopId/:branchId/create/text-template')
  @ApiParam({ name: 'shopId', required: true, type: String })
  @ApiParam({ name: 'branchId', required: true, type: String })
  @ApiOperation({ summary: 'Create a new text template' })
  @ApiBody({
    type: 'object',
    schema: {
      properties: {
        templateName: { type: 'string' },
        language: { type: 'string' },
        headerText: { type: 'string' },
        headerTextExample: { type: 'array', items: { type: 'string' } },
        bodyText: { type: 'string' },
        bodyTextExample: { type: 'array', items: { type: 'string' } },
        footerText: { type: 'string' },
        buttonsdata: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              type: {
                type: 'string',
                enum: ['URL'],
              },
              text: {
                type: 'string',
              },
              url: {
                type: 'string',
                format: 'uri',
              },
            },
          },
        },
      },
      required: [
        'templateName',
        'language',
        'headerText',
        'headerTextExample',
        'bodyText',
        'bodyTextExample',
        'buttonsdata',
      ],
    },
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'The message template has been created successfully.',
  })
  async createTextMessageTemplate(
    @Param('shopId') shopId: string,
    @Param('branchId') branchId: string,
    @Body() templateData: any,
  ) {
    try {
      const { businessAccountId, shopAccessToken } =
        await this.whatsappService.getBusinessAccountInfo(shopId);
      // Add your logic to handle the creation of message templates using `templateData`
      const createdTemplate =
        await this.whatsappService.createEditMessageTemplate(
          businessAccountId,
          shopAccessToken,
          templateData,
          shopId,
          branchId,
        );

      if (createdTemplate && createdTemplate.error) {
        throw new BadRequestException(createdTemplate.error);
      }
      return createdTemplate;
    } catch (error) {
      throw new BadRequestException(error?.response?.error_user_msg);
    }
  }

  @Put('/templates/:shopId/:branchId/create/text-template/:templateId')
  @ApiParam({ name: 'shopId', required: true, type: String })
  @ApiParam({ name: 'branchId', required: true, type: String })
  @ApiParam({ name: 'templateId', required: true, type: String })
  @ApiOperation({ summary: 'Edit a text template' })
  @ApiBody({
    type: 'object',
    schema: {
      properties: {
        templateName: { type: 'string' },
        language: { type: 'string' },
        headerText: { type: 'string' },
        headerTextExample: { type: 'array', items: { type: 'string' } },
        bodyText: { type: 'string' },
        bodyTextExample: { type: 'array', items: { type: 'string' } },
        footerText: { type: 'string' },
        buttonsdata: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              type: {
                type: 'string',
                enum: ['URL'],
              },
              text: {
                type: 'string',
              },
              url: {
                type: 'string',
                format: 'uri',
              },
            },
          },
        },
      },
      required: [
        'templateName',
        'language',
        'headerText',
        'headerTextExample',
        'bodyText',
        'bodyTextExample',
        'buttonsdata',
      ],
    },
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'The message template has been edited successfully.',
  })
  async editTextMessageTemplate(
    @Param('shopId') shopId: string,
    @Param('branchId') branchId: string,
    @Param('templateId') templateId: string,
    @Body() templateData: any,
  ) {
    try {
      const { businessAccountId, shopAccessToken } =
        await this.whatsappService.getBusinessAccountInfo(shopId);
      // Add your logic to handle the creation of message templates using `templateData`
      const createdTemplate =
        await this.whatsappService.createEditMessageTemplate(
          businessAccountId,
          shopAccessToken,
          templateData,
          shopId,
          branchId,
          templateId,
        );
      if (createdTemplate && createdTemplate.error) {
        throw new BadRequestException(createdTemplate.error);
      }
      return createdTemplate;
    } catch (error) {
      throw new BadRequestException(error?.response?.error_user_msg);
    }
  }

  @Post('/templates/:shopId/:branchId/create/image-template')
  @ApiParam({ name: 'shopId', required: true, type: String })
  @ApiParam({ name: 'branchId', required: true, type: String })
  @ApiOperation({ summary: 'Create a new image template' })
  @ApiBody({
    type: 'object',
    schema: {
      properties: {
        templateName: { type: 'string' },
        language: { type: 'string' },
        headerImageExample: { type: 'string' },
        bodyText: { type: 'string' },
        bodyTextExample: { type: 'array', items: { type: 'string' } },
        footerText: { type: 'string' },
        buttonsdata: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              type: {
                type: 'string',
                enum: ['URL'],
              },
              text: {
                type: 'string',
              },
              url: {
                type: 'string',
                format: 'uri',
              },
            },
          },
        },
      },
      required: [
        'templateName',
        'language',
        'headerImageExample',
        'bodyText',
        'bodyTextExample',
        'buttonsdata',
      ],
    },
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'The message template has been created successfully.',
  })
  async createImageMessageTemplate(
    @Param('shopId') shopId: string,
    @Param('branchId') branchId: string,
    @Body() templateData: any,
  ) {
    try {
      const { businessAccountId, shopAccessToken } =
        await this.whatsappService.getBusinessAccountInfo(shopId);

      // Add your logic to handle the creation of message templates using `templateData`
      const createdTemplate =
        await this.whatsappService.createEditImageMessageTemplate(
          businessAccountId,
          shopAccessToken,
          templateData,
          shopId,
          branchId,
        );

      if (createdTemplate && createdTemplate.error) {
        throw new BadRequestException(createdTemplate.error);
      }
      return createdTemplate;
    } catch (error) {
      throw new BadRequestException(error?.response?.error_user_msg);
    }
  }

  @Put('/templates/:shopId/:branchId/create/image-template/:templateId')
  @ApiParam({ name: 'shopId', required: true, type: String })
  @ApiParam({ name: 'branchId', required: true, type: String })
  @ApiParam({ name: 'templateId', required: true, type: String })
  @ApiOperation({ summary: 'Edit a image template' })
  @ApiBody({
    type: 'object',
    schema: {
      properties: {
        templateName: { type: 'string' },
        language: { type: 'string' },
        headerImageExample: { type: 'string' },
        bodyText: { type: 'string' },
        bodyTextExample: { type: 'array', items: { type: 'string' } },
        footerText: { type: 'string' },
        buttonsdata: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              type: {
                type: 'string',
                enum: ['URL'],
              },
              text: {
                type: 'string',
              },
              url: {
                type: 'string',
                format: 'uri',
              },
            },
          },
        },
      },
      required: [
        'templateName',
        'language',
        'headerImageExample',
        'bodyText',
        'bodyTextExample',
        'buttonsdata',
      ],
    },
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'The message template has been edited successfully.',
  })
  async editImageMessageTemplate(
    @Param('shopId') shopId: string,
    @Param('templateId') templateId: string,
    @Param('branchId') branchId: string,
    @Body() templateData: any,
  ) {
    try {
      const { businessAccountId, shopAccessToken } =
        await this.whatsappService.getBusinessAccountInfo(shopId);
      // Add your logic to handle the creation of message templates using `templateData`
      const createdTemplate =
        await this.whatsappService.createEditImageMessageTemplate(
          businessAccountId,
          shopAccessToken,
          templateData,
          shopId,
          branchId,
          templateId,
        );
      if (createdTemplate && createdTemplate.error) {
        throw new BadRequestException(createdTemplate.error);
      }
      return createdTemplate;
    } catch (error) {
      throw new BadRequestException(error?.response?.error_user_msg);
    }
  }

  @Post('/templates/:shopId/:branchId/create/doc-template')
  @ApiParam({ name: 'shopId', required: true, type: String })
  @ApiParam({ name: 'branchId', required: true, type: String })
  @ApiOperation({ summary: 'Create a new document template' })
  @ApiBody({
    type: 'object',
    schema: {
      properties: {
        templateName: { type: 'string' },
        language: { type: 'string' },
        headerDocumentExample: { type: 'string' },
        bodyText: { type: 'string' },
        bodyTextExample: { type: 'array', items: { type: 'string' } },
        footerText: { type: 'string' },
        buttonsdata: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              type: {
                type: 'string',
                enum: ['URL'],
              },
              text: {
                type: 'string',
              },
              url: {
                type: 'string',
                format: 'uri',
              },
            },
          },
        },
      },
      required: [
        'templateName',
        'language',
        'headerDocumentExample',
        'bodyText',
        'bodyTextExample',
        'buttonsdata',
      ],
    },
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'The message template has been created successfully.',
  })
  async createDocMessageTemplate(
    @Param('shopId') shopId: string,
    @Param('branchId') branchId: string,
    @Body() templateData: any,
  ) {
    try {
      const { businessAccountId, shopAccessToken } =
        await this.whatsappService.getBusinessAccountInfo(shopId);
      // Add your logic to handle the creation of message templates using `templateData`
      const createdTemplate =
        await this.whatsappService.createEditDocMessageTemplate(
          businessAccountId,
          shopAccessToken,
          templateData,
          shopId,
          branchId,
        );
      if (createdTemplate && createdTemplate.error) {
        throw new BadRequestException(createdTemplate.error);
      }
      return createdTemplate;
    } catch (error) {
      throw new BadRequestException(error?.response?.error_user_msg);
    }
  }

  @Put('/templates/:shopId/:branchId/create/doc-template/:templateId')
  @ApiParam({ name: 'shopId', required: true, type: String })
  @ApiParam({ name: 'branchId', required: true, type: String })
  @ApiParam({ name: 'templateId', required: true, type: String })
  @ApiOperation({ summary: 'Edit a doc template' })
  @ApiBody({
    type: 'object',
    schema: {
      properties: {
        templateName: { type: 'string' },
        language: { type: 'string' },
        headerDocumentExample: { type: 'string' },
        bodyText: { type: 'string' },
        bodyTextExample: { type: 'array', items: { type: 'string' } },
        footerText: { type: 'string' },
        buttonsdata: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              type: {
                type: 'string',
                enum: ['URL'],
              },
              text: {
                type: 'string',
              },
              url: {
                type: 'string',
                format: 'uri',
              },
            },
          },
        },
      },
      required: [
        'templateName',
        'language',
        'headerDocumentExample',
        'bodyText',
        'bodyTextExample',
        'buttonsdata',
      ],
    },
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'The message template has been edited successfully.',
  })
  async editDocMessageTemplate(
    @Param('shopId') shopId: string,
    @Param('branchId') branchId: string,
    @Param('templateId') templateId: string,
    @Body() templateData: any,
  ) {
    try {
      const { businessAccountId, shopAccessToken } =
        await this.whatsappService.getBusinessAccountInfo(shopId);
      // Add your logic to handle the creation of message templates using `templateData`
      const createdTemplate =
        await this.whatsappService.createEditDocMessageTemplate(
          businessAccountId,
          shopAccessToken,
          templateData,
          shopId,
          branchId,
          templateId,
        );
      if (createdTemplate && createdTemplate.error) {
        throw new BadRequestException(createdTemplate.error);
      }
      return createdTemplate;
    } catch (error) {
      throw new BadRequestException(error?.response?.error_user_msg);
    }
  }

  // New DELETE method for deleting image template
  @Delete('/templates/delete/:shopId/:templateId/:templateName')
  @ApiParam({ name: 'templateId', required: true, type: String })
  @ApiParam({ name: 'templateName', required: true, type: String })
  @ApiOperation({ summary: 'Delete an  template' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'The  template has been deleted successfully.',
  })
  async deleteImageTemplate(
    @Param('shopId') shopId: string,
    // @Param('branchId') branchId: string,
    @Param('templateId') templateId: string,
    @Param('templateName') templateName: string,
  ) {
    try {
      const { businessAccountId, shopAccessToken } =
        await this.whatsappService.getBusinessAccountInfo(shopId);

      const deletedTemplate = await this.whatsappService.deleteMessageTemplate(
        businessAccountId,
        shopAccessToken,
        templateId,
        templateName,
        shopId,
        // branchId,
      );

      if (deletedTemplate && deletedTemplate.error) {
        throw new BadRequestException(deletedTemplate.error);
      }
      return deletedTemplate;
    } catch (error) {
      throw new BadRequestException(error?.response?.error_user_msg);
    }
  }

  @Post('/get-file-handler/:shopId')
  @UseInterceptors(FileInterceptor('file'))
  @ApiParam({
    name: 'shopId',
    description: 'ID of the club',
    type: 'string',
  })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'returns the  File handler key',
  })
  @ApiConsumes('multipart/form-data')
  async uploadFile(
    @UploadedFile('file') file: any,
    @Param('shopId') shopId: string,
  ) {
    try {
      const { shopAccessToken, shopAppId } =
        await this.whatsappService.getBusinessAccountInfo(shopId);

      const { size: fileLength, mimetype: fileType } = file;

      const fileDataRes = await this.whatsappService.initiateUpload(
        shopAppId,
        fileLength,
        fileType,
        shopAccessToken,
        file,
      );

      if (fileDataRes && fileDataRes.error) {
        throw new BadRequestException(fileDataRes.error);
      }
      return { fileHandlerKey: fileDataRes };
    } catch (error) {
      throw new BadRequestException(error?.response?.error_user_msg);
    }
  }

  @Post('/audience/:shopId/:branchId')
  @ApiParam({ name: 'branchId', required: true, type: String })
  @ApiParam({ name: 'shopId', required: true, type: String })
  @ApiOperation({ summary: 'Create audience data' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        audienceType: { type: 'string' },
        audienceData: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              phoneNumber: { type: 'string' },
              name: { type: 'string' },
            },
          },
        },
      },
      required: ['audienceType', 'audienceData'],
    },
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Audience data created successfully.',
  })
  async createAudience(
    @Param('shopId') shopId: string,
    @Param('branchId') branchId: string,
    @Body() audiencePayload: { audienceType: string; audienceData: any[] },
  ) {
    try {
      const createdAudience = await this.whatsappService.createAudience(
        shopId,
        branchId,
        audiencePayload,
      );
      return createdAudience;
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  @Put('/audience/:shopId')
  // @ApiParam({ name: 'branchId', required: true, type: String })
  @ApiParam({ name: 'shopId', required: true, type: String })
  @ApiOperation({ summary: 'Update New audiences in existing data' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        audienceTypes: { type: 'array', items: { type: 'string' } },
        audienceData: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              phoneNumber: { type: 'string' },
              name: { type: 'string' },
            },
          },
        },
      },
      required: ['audienceTypes', 'audienceData'],
    },
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'New Audience data updated successfully.',
  })
  async editAudience(
    @Param('shopId') shopId: string,
    @Body() audiencePayload: { audienceTypes: string[]; audienceData: any[] },
  ) {
    try {
      const createdAudience = await this.whatsappService.updateAudience(
        shopId,
        audiencePayload,
      );
      return createdAudience;
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  @Get('/audience/:shopId/:type')
  @ApiParam({ name: 'shopId', required: true, type: String })
  @ApiParam({ name: 'type', required: true, type: String })
  @ApiQuery({ name: 'branchId', required: false, type: String })
  @ApiQuery({ name: 'audienceTypeId', required: false, type: String })
  @ApiQuery({
    name: 'filter',
    enum: ['all', 'audiences', 'customers', 'tags'],
    required: false,
  })
  @ApiOperation({ summary: 'Get all audiences' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'List of all audiences',
  })
  async getAllAudiences(
    @Param('shopId') shopId: string,
    @Param('type') type: string,
    @Query('branchId') branchId?: string,
    @Query('audienceTypeId') audienceTypeId?: string,
    @Query('filter') filter?: string,
  ) {
    try {
      const audiences = await this.whatsappService.getAudiencesData(
        shopId,
        type,
        this.whatsappService.normalizeToNull(branchId) || '',
        this.whatsappService.normalizeToNull(audienceTypeId) || '',
        filter ? filter : 'all',
      );
      return audiences;
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  // New DELETE method for deleting entire audience and its mapping with audiences
  @Delete('/audience/delete-audience-type/:shopId/:audienceTypeId')
  @ApiParam({ name: 'shopId', required: true, type: String })
  @ApiParam({ name: 'audienceTypeId', required: true, type: String })
  @ApiOperation({ summary: 'Delete an  Audience type' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'The Audience type has been deleted successfully.',
  })
  async deleteAudienceType(
    @Param('shopId') shopId: string,
    @Param('branchId') branchId: string,
    @Param('audienceTypeId') audienceTypeId: string,
  ) {
    try {
      const deleteAudienceType = await this.whatsappService.deleteAudienceType(
        shopId,
        audienceTypeId,
      );
      if (deleteAudienceType && deleteAudienceType.error) {
        throw new BadRequestException(deleteAudienceType.error);
      }
      return { message: 'The Audience type has been deleted successfully.' };
    } catch (error) {
      throw new BadRequestException(error?.response?.error_user_msg);
    }
  }

  @Post('/audience/delete-audiences')
  @ApiOperation({ summary: 'Delete an Audience/ multiple audiences' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'The Audience has been deleted successfully.',
  })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        shopId: {
          type: 'string',
        },
        selectedAudienceData: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              audience_id: {
                type: 'string',
              },
              branch_id: {
                type: 'string',
              },
            },
            required: ['audience_id', 'branch_id'],
          },
        },
      },
      required: ['selectedAudienceData'],
    },
  })
  async deleteAudience(
    @Body()
    audiencePayload: any,
  ) {
    try {
      const { selectedAudienceData, shopId } = audiencePayload;
      await this.whatsappService.deleteAudience(shopId, selectedAudienceData);

      return { message: 'The Audience has been deleted successfully.' };
    } catch (error) {
      throw new BadRequestException(error);
    }
  }

  @Post('/broadcast/:shopId')
  @ApiParam({ name: 'shopId', required: true, type: String })
  @ApiOperation({ summary: 'Store Broadcast Details to send messages. ' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        templateDetails: {
          type: 'object',
          properties: {
            templateName: { type: 'string' },
            templateLanguage: { type: 'string' },
            templateType: { type: 'string' },
            imageOrDocURL: { type: 'string' },
            headerParameters: { type: 'string' },
            bodyParameters: { type: 'string' },
          },
        },
        selectedAudienceData: {
          type: 'array',
          items: {
            type: 'string',
          },
        },
      },
      required: ['templateDetails', 'selectedAudienceData'],
    },
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Broadcast has been queued successfully.',
  })
  async storeBoradCastInfo(
    @Param('shopId') shopId: string,
    @Body()
    broadcastPayload: {
      templateDetails: {
        templateName: string;
        templateLanguage: string;
        templateType: string;
        imageOrDocURL: string;
        headerParameters: string;
        bodyParameters: string;
      };
      selectedAudienceData: string[];
    },
  ) {
    try {
      const storeBoradCastInfoRes =
        await this.whatsappService.storeBoradCastInfo(shopId, broadcastPayload);
      return storeBoradCastInfoRes;
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }
  @Get('/templates/analytics/:shopId/:startTime/:endTime/:templateId')
  @ApiParam({ name: 'shopId', required: true, type: String })
  @ApiParam({ name: 'startTime', required: true, type: String || Number })
  @ApiParam({ name: 'endTime', required: true, type: String || Number })
  @ApiParam({ name: 'templateId', required: true, type: String })
  @ApiOperation({ summary: 'Get Template analytics from META' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Template analytics from META.',
  })
  async getTemplateAnalytics(
    @Param('shopId') shopId: string,
    @Param('startTime') startTime: string,
    @Param('endTime') endTime: string,
    @Param('templateId') templateId: string,
  ) {
    try {
      const { businessAccountId, shopAccessToken } =
        await this.whatsappService.getBusinessAccountInfo(shopId);
      if (businessAccountId && shopAccessToken) {
        const templateData = await this.whatsappService.getTemplateAnalytics(
          {
            businessAccountId,
            shopAccessToken,
          },
          startTime,
          endTime,
          templateId,
        );
        const cummulativeData =
          await this.whatsappService.getCummulativeTemplateAnalytics(
            templateData,
            false,
          );

        const { newStartTime, newEndTime, daysDifference } =
          await this.whatsappService.calculateNewEpochStartEnd(
            startTime,
            endTime,
          );

        let templateData2 = {};
        let donotCalcCum = false;
        if (daysDifference !== 90 && daysDifference !== 60) {
          templateData2 = await this.whatsappService.getTemplateAnalytics(
            {
              businessAccountId,
              shopAccessToken,
            },
            newStartTime,
            newEndTime,
            templateId,
          );
          donotCalcCum = false;
        } else {
          templateData2 = templateData;
          donotCalcCum = true;
        }
        const cummulativeData2 =
          await this.whatsappService.getCummulativeTemplateAnalytics(
            templateData2,
            donotCalcCum,
          );
        const performance = await this.whatsappService.calculateImprovements(
          cummulativeData, // new
          cummulativeData2, // old
        );
        return {
          dailyData: templateData.data,
          cummulativeData: performance,
        };
      } else {
        return false;
      }
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  @Get('/analytics/all-message-analytics/:shopId/:startTime/:endTime')
  @ApiParam({ name: 'shopId', required: true, type: String })
  @ApiParam({ name: 'startTime', required: true, type: String })
  @ApiParam({ name: 'endTime', required: true, type: String })
  @ApiOperation({ summary: 'Get all message analaytics from META' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'The list  message analaytics from META.',
  })
  async getAllMessageAnalytics(
    @Param('shopId') shopId: string,
    @Param('startTime') startTime: string,
    @Param('endTime') endTime: string,
  ) {
    try {
      const { businessAccountId, shopAccessToken } =
        await this.whatsappService.getBusinessAccountInfo(shopId);
      if (businessAccountId && shopAccessToken) {
        const templateData = await this.whatsappService.getAllAnalytics(
          {
            businessAccountId,
            shopAccessToken,
          },
          startTime,
          endTime,
        );
        const cummulativeData =
          await this.whatsappService.getCummulativeAllAnalytics(
            templateData,
            false,
          );
        const { newStartTime, newEndTime, daysDifference } =
          await this.whatsappService.calculateNewEpochStartEnd(
            startTime,
            endTime,
          );
        let templateData2 = {};
        let donotCalcCum = false;

        if (daysDifference !== 90 && daysDifference !== 60) {
          templateData2 = await this.whatsappService.getAllAnalytics(
            {
              businessAccountId,
              shopAccessToken,
            },
            newStartTime,
            newEndTime,
          );
          donotCalcCum = false;
        } else {
          donotCalcCum = true;
          templateData2 = templateData;
        }
        const cummulativeData2 =
          await this.whatsappService.getCummulativeAllAnalytics(
            templateData2,
            donotCalcCum,
          );
        const performance = await this.whatsappService.calculateAllImprovements(
          cummulativeData, // new
          cummulativeData2, // old
        );
        return {
          dailyData: templateData?.analytics?.data_points,
          cummulativeData: performance,
        };
      } else {
        return false;
      }
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }
  @Get('/analytics/all-conversation-analytics/:shopId/:startTime/:endTime')
  @ApiParam({ name: 'shopId', required: true, type: String })
  @ApiParam({ name: 'startTime', required: true, type: String })
  @ApiParam({ name: 'endTime', required: true, type: String })
  @ApiOperation({ summary: 'Get all conversation analaytics from META' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'The list  conversation analaytics from META.',
  })
  async getAllConversationAnalytics(
    @Param('shopId') shopId: string,
    @Param('startTime') startTime: string,
    @Param('endTime') endTime: string,
  ) {
    try {
      const { businessAccountId, shopAccessToken } =
        await this.whatsappService.getBusinessAccountInfo(shopId);
      if (businessAccountId && shopAccessToken) {
        const templateData =
          await this.whatsappService.getAllConversationAnalytics(
            {
              businessAccountId,
              shopAccessToken,
            },
            startTime,
            endTime,
          );
        const cummulativeData =
          await this.whatsappService.getCummulativeAllConversationAnalytics(
            templateData,
            false,
          );
        const { newStartTime, newEndTime, daysDifference } =
          await this.whatsappService.calculateNewEpochStartEnd(
            startTime,
            endTime,
          );
        let templateData2 = {};
        let donotCalcCum = false;

        if (daysDifference !== 90 && daysDifference !== 60) {
          templateData2 =
            await this.whatsappService.getAllConversationAnalytics(
              {
                businessAccountId,
                shopAccessToken,
              },
              newStartTime,
              newEndTime,
            );
          donotCalcCum = false;
        } else {
          templateData2 = templateData;
          donotCalcCum = true;
        }
        const cummulativeData2 =
          await this.whatsappService.getCummulativeAllConversationAnalytics(
            templateData2,
            donotCalcCum,
          );
        const performance =
          await this.whatsappService.calculateConversationImprovements(
            cummulativeData, // new
            cummulativeData2, // old
          );
        return {
          dailyData: templateData?.conversation_analytics?.data[0]?.data_points,
          cummulativeData: performance,
        };
      } else {
        return false;
      }
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  // Create  a flow
  @Post('/create-address-form/:shopId')
  @ApiParam({ name: 'shopId', required: true, type: String })
  // @ApiParam({ name: 'branchId', required: true, type: String })
  @ApiOperation({ summary: 'Create a new text template' })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'The message template has been created successfully.',
  })
  async createFlow(@Param('shopId') shopId: string) {
    try {
      const { businessAccountId, shopAccessToken } =
        await this.whatsappService.getBusinessAccountInfo(shopId);
      // Add your logic to handle the creation of message templates using `templateData`
      const createdFlow = await this.whatsappService.createFlow(
        shopId,
        businessAccountId,
        shopAccessToken,
      );

      if (createdFlow && createdFlow.error) {
        throw new BadRequestException(createdFlow.error);
      }
      return createdFlow;
    } catch (error) {
      throw new BadRequestException(error?.response?.error_user_msg);
    }
  }

  // update audience name
  @Patch('/audience/:shopId/:phone/:name')
  @ApiOperation({ summary: 'Update name of audience based on phone number ' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Update name of audience based on phone number',
    type: Boolean,
  })
  @ApiParam({
    name: 'shopId',
    description: 'Shop Id',
    type: String,
  })
  @ApiParam({
    name: 'phone',
    description: 'Audience phone number',
    type: String,
  })
  @ApiParam({
    name: 'name',
    description: 'New Name',
    type: String,
  })
  async changeAudienceName(
    @Param('shopId') shopId: string,
    @Param('phone') phone: string,
    @Param('name') name: string,
  ) {
    return this.whatsappService.changeAudienceName(shopId, phone, name);
  }

  @Delete('/audience/:shopId/:audienceType/:audienceId')
  @ApiOperation({ summary: 'Delete audience' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Update name of audience based on phone number',
    type: Boolean,
  })
  @ApiParam({
    name: 'shopId',
    description: 'Shop Id',
    type: String,
  })
  @ApiParam({
    name: 'audienceId',
    description: 'Audience ID',
    type: String,
  })
  @ApiParam({
    name: 'audienceType',
    description: 'Audience Type',
    type: String,
  })
  async deleteAudienceInAudienceType(
    @Param('shopId') shopId: string,
    @Param('audienceId') audienceId: string,
    @Param('audienceType') audienceType: string,
  ) {
    return this.whatsappService.deleteAudienceInAudienceType(
      shopId,
      audienceId,
      audienceType,
    );
  }

  @Get('/broadcast-history/:templateName/:shopId')
  @ApiParam({ name: 'templateName', required: true, type: String })
  @ApiParam({ name: 'shopId', required: true, type: String })
  // @ApiParam({ name: 'branchId', required: true, type: String })
  @ApiOperation({ summary: 'Get broadcast history' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'List of broadcast history records',
  })
  async getBroadCastHistory(
    @Param('templateName') templateName: string,
    @Param('shopId') shopId: string,
    // @Param('branchId') branchId: string,
  ) {
    try {
      const broadcastHistory = await this.whatsappService.getBroadcastHistory(
        templateName,
        shopId,
        // branchId === 'null' ? '' : branchId,
      );
      return broadcastHistory;
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  @Get('/campaign-phone-numbers/:broadCastId')
  @ApiParam({ name: 'broadCastId', required: true, type: String })
  @ApiOperation({
    summary: 'Get phone number list based on broadcast campaign ID',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'List of phone numbers associated with the broadcast campaign',
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid request or error occurred',
  })
  async getCampaignPhoneNumbers(@Param('broadCastId') broadCastId: string) {
    try {
      const phoneNumberList =
        await this.whatsappService.getPhoneNumbersByBroadcastMsgId(broadCastId);
      return phoneNumberList;
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  @Post('/move-audience/:shopId')
  @ApiParam({ name: 'shopId', required: true, type: String })
  @ApiOperation({ summary: 'Move audiences from one Audience to another' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        fromAudienceId: { type: 'string' },
        toAudienceId: { type: 'string' },
        audienceIds: {
          type: 'array',
          items: {
            type: 'string',
          },
        },
      },
      required: ['fromAudienceId', 'toAudienceId', 'audienceIds'],
    },
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Audience data moved successfully.',
  })
  async moveAudience(
    @Param('shopId') shopId: string,
    @Body()
    audiencePayload: {
      fromAudienceId: string;
      toAudienceId: string;
      audienceIds: string[];
    },
  ) {
    try {
      const movedAudience = await this.whatsappService.moveAudience(
        shopId,
        audiencePayload.fromAudienceId,
        audiencePayload.toAudienceId,
        audiencePayload.audienceIds,
      );
      return movedAudience;
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  @Post('/copy-audience/:shopId')
  @ApiParam({ name: 'shopId', required: true, type: String })
  @ApiOperation({
    summary: 'Copy audience members from one Audience to another',
  })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        fromAudienceId: { type: 'string' },
        toAudienceId: { type: 'string' },
        audienceIds: {
          type: 'array',
          items: {
            type: 'string',
          },
        },
      },
      required: ['fromAudienceId', 'toAudienceId', 'audienceIds'],
    },
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Audience data copied successfully.',
  })
  async copyAudience(
    @Param('shopId') shopId: string,
    @Body()
    audiencePayload: {
      fromAudienceId: string;
      toAudienceId: string;
      audienceIds: string[];
    },
  ) {
    try {
      const copiedAudience = await this.whatsappService.copyAudience(
        shopId,
        audiencePayload.fromAudienceId,
        audiencePayload.toAudienceId,
        audiencePayload.audienceIds,
      );
      return copiedAudience;
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  @Public()
  @Post('/get-audiences-by-types/:shopId')
  @ApiParam({ name: 'shopId', required: true, type: String })
  @ApiOperation({ summary: 'Get audience data by multiple types' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        selectedTypes: {
          type: 'array',
          items: { type: 'string' },
          description: 'Array of audience type IDs, tag IDs, or special types',
        },
        type: {
          type: 'string',
          enum: ['or', 'and'],
          description: 'Whether to get all numbers or only common numbers',
        },
      },
      required: ['selectedTypes', 'type'],
    },
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'List of audience members',
  })
  async getAudiencesByTypes(
    @Param('shopId') shopId: string,
    @Body()
    audiencePayload: {
      selectedTypes: string[];
      type: 'or' | 'and';
    },
  ) {
    try {
      const audiences = await this.whatsappService.getAudiencesByTypes(
        shopId,
        audiencePayload,
      );
      return audiences;
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }
  @Public()
  @Post('/get-audiences-count-by-types/:shopId')
  @ApiParam({ name: 'shopId', required: true, type: String })
  @ApiOperation({ summary: 'Get audience count by multiple types' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        selectedTypes: {
          type: 'array',
          items: { type: 'string' },
          description: 'Array of audience type IDs, tag IDs, or special types',
        },
      },
      required: ['selectedTypes'],
    },
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Count of audience members for AND/OR conditions',
  })
  async getAudiencesCountByTypes(
    @Param('shopId') shopId: string,
    @Body()
    audiencePayload: {
      selectedTypes: string[];
    },
  ) {
    try {
      const counts = await this.whatsappService.getAudiencesCountByTypes(
        shopId,
        audiencePayload,
      );
      return counts;
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  @Public()
  @Post('/store-broadcast/:shopId')
  @ApiParam({ name: 'shopId', required: true, type: String })
  @ApiOperation({ summary: 'Store broadcast with audience types' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        templateDetails: {
          type: 'object',
          properties: {
            templateName: { type: 'string' },
            templateLanguage: { type: 'string' },
            templateType: { type: 'string' },
            imageOrDocURL: { type: 'string' },
            headerParameters: { type: 'string' },
            bodyParameters: { type: 'string' },
          },
          required: ['templateName', 'templateLanguage', 'templateType'],
        },
        audiencesSelected: {
          type: 'array',
          items: { type: 'string' },
          description: 'Array of audience type IDs, tag IDs, or special types',
        },
        selectionType: {
          type: 'string',
          enum: ['AND', 'OR'],
          description: 'Whether to include all audiences or only common ones',
        },
        excludedContacts: {
          type: 'array',
          items: { type: 'string' },
          description: 'Array of phone numbers to exclude from the broadcast',
        },
        scheduleType: {
          type: 'string',
          description: 'Type of scheduling (e.g., "now", "later")',
        },
        scheduledDateTime: {
          type: 'string',
          description: 'ISO date-time string for scheduled broadcasts',
        },
      },
      required: ['templateDetails', 'audiencesSelected', 'selectionType'],
    },
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Broadcast has been stored successfully.',
  })
  async storeBroadcast(
    @Param('shopId') shopId: string,
    @Body()
    payload: {
      templateDetails: {
        templateName: string;
        templateLanguage: string;
        templateType: string;
        imageOrDocURL?: string;
        headerParameters?: string;
        bodyParameters?: string;
      };
      audiencesSelected: string[];
      selectionType: 'AND' | 'OR';
      excludedContacts?: string[];
      scheduleType?: string;
      scheduledDateTime?: string;
    },
  ) {
    try {
      const broadcast = await this.whatsappService.storeBroadcast(
        shopId,
        payload,
      );
      return broadcast;
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new BadRequestException(
        'Failed to store broadcast: ' + error.message,
      );
    }
  }

  @Get('/get-whatsapp-business-details/:shopId')
  @ApiParam({ name: 'shopId', required: true, type: String })
  @ApiOperation({ summary: 'Get whatsapp business account info' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Whatsapp business account info',
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid request or error occurred',
  })
  async getWhatsappBusinessInfo(@Param('shopId') shopId: string) {
    try {
      const whatsappBusinessInfo =
        await this.whatsappService.getBusinessDetails(shopId);
      return whatsappBusinessInfo;
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  @Post('/download-media/:shopId')
  @ApiParam({ name: 'shopId', required: true, type: String })
  @ApiOperation({ summary: 'Download Meta media files' })
  @ApiBody({ type: DownloadMediaDto })
  @Header('Cache-Control', 'public, max-age=3600')
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Media file downloaded successfully',
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid request or error occurred',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Media not found',
  })
  async downloadMetaMedia(
    @Param('shopId') shopId: string,
    @Body() downloadMediaDto: DownloadMediaDto,
  ): Promise<StreamableFile> {
    try {
      const { mediaId, download = false } = downloadMediaDto;

      if (!mediaId) {
        throw new BadRequestException('Media ID is required');
      }

      // Get access token from database based on shopId
      const { shopAccessToken } = await this.whatsappService.getBusinessAccountInfo(shopId);

      if (!shopAccessToken) {
        throw new BadRequestException('Shop access token not found');
      }

      // Download media using the service
      const mediaData = await this.whatsappService.downloadMetaMedia(mediaId, shopAccessToken);

      // Create StreamableFile with proper headers
      const file = new StreamableFile(mediaData.buffer, {
        type: mediaData.mimeType,
        disposition: download ? `attachment; filename="${mediaData.fileName}"` : undefined,
      });

      return file;
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new BadRequestException('Failed to download media: ' + error.message);
    }
  }
}
