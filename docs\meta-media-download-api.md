# Meta Media Download API

## Overview
This API endpoint allows you to download media files from Meta's WhatsApp Business API using the shop's stored access token.

## Endpoint
```
POST /commerce/wa-business-management/download-media/{shopId}
```

## Parameters

### Path Parameters
- `shopId` (string, required): The ID of the shop

### Request Body
```json
{
  "mediaId": "string",     // Required: Meta media ID to download
  "download": boolean      // Optional: Whether to force download as attachment (default: false)
}
```

## Response
- **Success (200)**: Returns the media file with appropriate headers
- **Bad Request (400)**: Invalid request or missing parameters
- **Not Found (404)**: Media not found

## Headers Set in Response
- `Content-Type`: The MIME type of the media file
- `Content-Length`: Size of the file in bytes
- `Cache-Control`: `public, max-age=3600` (1 hour cache)
- `Content-Disposition`: Set to `attachment` if `download=true`

## Supported Media Types
- Images: JPEG, PNG, GIF, WebP
- Audio: MP3, M4A, OGG, WAV
- Video: MP4, MOV, WebM
- Documents: PDF, DOC, DOCX, TXT

## Example Usage

### JavaScript/Fetch
```javascript
const response = await fetch('/commerce/wa-business-management/download-media/shop123', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer your-jwt-token'
  },
  body: JSON.stringify({
    mediaId: 'media123456',
    download: true
  })
});

if (response.ok) {
  const blob = await response.blob();
  const url = window.URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = 'downloaded-media';
  a.click();
}
```

### cURL
```bash
curl -X POST \
  'http://localhost:3000/commerce/wa-business-management/download-media/shop123' \
  -H 'Content-Type: application/json' \
  -H 'Authorization: Bearer your-jwt-token' \
  -d '{
    "mediaId": "media123456",
    "download": true
  }' \
  --output downloaded-media.jpg
```

## How It Works
1. The API receives the shopId and mediaId
2. It retrieves the shop's WhatsApp access token from the database
3. It calls Meta's Graph API to get the media URL
4. It downloads the actual media file from Meta's servers
5. It returns the file to the user with appropriate headers

## Error Handling
- Returns 400 if mediaId is missing
- Returns 400 if shop access token is not found
- Returns 400 if Meta API calls fail
- Returns 500 for internal server errors

## Security
- Requires JWT authentication
- Uses shop-specific access tokens stored securely in the database
- No direct access token exposure in the API
