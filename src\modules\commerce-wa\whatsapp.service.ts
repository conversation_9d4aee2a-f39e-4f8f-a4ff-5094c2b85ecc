import { BadRequestException, Injectable } from '@nestjs/common';
import { DatabaseService } from '../../common/config/database.services';
import axios from 'axios';

const EPOCH_MS_PER_DAY = 86400; // Milliseconds in one day

@Injectable()
export class CommerceWhatsappService {
  constructor(private readonly databaseService: DatabaseService) {}
  async getBusinessAccountInfo(shopInfo: string) {
    try {
      const sqlQuery = `
      SELECT shop_business_account_id business_account_id, 
      shop_whatsapp_token access_key,
      shop_whatsapp_app_id whatsapp_app_id
       FROM public."Shops" 
      WHERE shop_id = $1;`;
      const shopDetails = await this.databaseService.query(sqlQuery, [
        shopInfo,
      ]);
      if (shopDetails) {
        return {
          businessAccountId: shopDetails[0].business_account_id,
          shopAccessToken: shopDetails[0].access_key,
          shopAppId: shopDetails[0].whatsapp_app_id,
        };
      } else {
        return {
          businessAccountId: null,
          shopAccessToken: null,
          shopAppId: null,
        };
      }
    } catch (error) {}
  }

  async getTemplates(
    shopInfo: {
      businessAccountId: string;
      shopAccessToken: string;
    },
    shopId: string,
    branchIdValue: string,
  ) {
    let branchId = branchIdValue;
    const branchesQuery = `  
    SELECT branch_id
    FROM public."ShopBranches"
    WHERE fk_shop_id = $1
  `;
    const exists = await this.databaseService.query(branchesQuery, [shopId]);
    if (exists.length === 1) {
      branchId = null;
    }
    const { businessAccountId, shopAccessToken } = shopInfo;
    try {
      const config = {
        method: 'get',
        url: `https://graph.facebook.com/v18.0/${businessAccountId}/message_templates?category=['MARKETING']`,
        headers: {
          Authorization: `Bearer ${shopAccessToken}`,
          'Content-Type': 'application/json',
        },
      };

      const res = await axios(config);
      const allMetaTemp = res?.data?.data;
      if (!allMetaTemp) {
        return null;
      }

      const params = [shopId];
      let sqlQuery = `
        SELECT wa_template_id, fk_branch_id,branch_name,COALESCE(template_quality,'') template_quality
        FROM public."WATemplateIdBranchMapping" wa
          LEFT  JOIN public."ShopBranches" br ON br.fk_shop_id = wa.fk_shop_id
			 	    AND branch_id = fk_branch_id
        WHERE wa.fk_shop_id = $1
      `;

      if (branchId) {
        sqlQuery += ` AND fk_branch_id = $2`;
        params.push(branchId);
      }

      const shopTemplateDetails = await this.databaseService.query(
        sqlQuery,
        params,
      );
      const templateBranchMap = new Map(
        shopTemplateDetails.map((each) => [
          each.wa_template_id,
          each.fk_branch_id,
        ]),
      );
      const templateBranchNameMap = new Map(
        shopTemplateDetails.map((each) => [
          each.wa_template_id,
          each.branch_name,
        ]),
      );

      const templateQualityMap = new Map(
        shopTemplateDetails.map((each) => [
          each.wa_template_id,
          each.template_quality,
        ]),
      );

      // Query to get running broadcast counts for each template
      const broadcastCountQuery = `
          SELECT 
          b.template_name,
          COUNT(DISTINCT b.broadcast_msg_id)  AS running_broadcasts_count
      FROM 
          public."BroadcastMessagesData" b
      JOIN 
          public."BroadcastMessageAudience" a
          ON b.broadcast_msg_id = a.fk_broadcast_msg_id
      WHERE 
          a.status = 'Pending'
        AND b.fk_shop_id = $1
      GROUP BY 
          b.template_name
      `;

      const broadcastCounts = await this.databaseService.query(
        broadcastCountQuery,
        [shopId],
      );

      const templateBroadcastMap = new Map(
        broadcastCounts.map((each) => [
          each.template_name,
          parseInt(each.running_broadcasts_count) || 0,
        ]),
      );

      const filteredTemplates = allMetaTemp
        .filter((template) => templateBranchMap.has(template.id))
        .map((template) => ({
          ...template,
          branchId: templateBranchMap.get(template.id),
          branch_name: templateBranchNameMap.get(template.id),
          template_quality:
            template?.status === 'APPROVED'
              ? templateQualityMap.get(template.id) ||
                'Active - Quality Pending'
              : 'Pending',
          running_broadcasts_count:
            templateBroadcastMap.get(template.name) || 0,
        }));

      return filteredTemplates;
    } catch (err) {
      return err?.response?.data;
    }
  }

  async createEditMessageTemplate(
    businessAccountId: string,
    shopAccessToken: string,
    templateData: any,
    shopId: string,
    branchId: string,
    templateId?: string,
  ) {
    try {
      const {
        templateName,
        language,
        headerText,
        headerTextExample,
        bodyText,
        bodyTextExample,
        footerText,
        buttonsdata,
      } = templateData;
      const bodyTextExam = [];

      const header = {
        type: 'HEADER',
        format: 'TEXT',
        text: headerText,
      };
      const body = {
        type: 'BODY',
        text: bodyText,
      };
      if (headerTextExample.length > 0) {
        header['example'] = {
          header_text: headerTextExample,
        };
      }
      if (bodyTextExample.length > 0) {
        bodyTextExam.push(bodyTextExample);
        body['example'] = {
          body_text: bodyTextExam,
        };
      }

      const components: any = [];

      components.push(header);
      components.push(body);
      if (footerText.trim() !== '') {
        components.push({
          type: 'FOOTER',
          text: footerText,
        });
      }

      if (buttonsdata.length > 0) {
        components.push({
          type: 'BUTTONS',
          buttons: buttonsdata,
        });
      }
      const templateBody = {
        name: templateName,
        language: language,
        category: 'MARKETING',
        components,
      };
      const config = {
        method: 'post',
        url: `https://graph.facebook.com/v18.0/${businessAccountId}/message_templates${
          templateId ? '/' + templateId : ''
        }`,
        headers: {
          Authorization: `Bearer ${shopAccessToken}`,
          'Content-Type': 'application/json',
        },
        data: templateBody,
      };

      const res = await axios(config);
      if (res?.data) {
        const tempId = res.data.id;
        this.updateTemplateMapping(shopId, branchId, tempId);
      }
      return res?.data;
    } catch (err) {
      return err.response.data; // Assuming you want to return the error response data
    }
  }

  async createEditImageMessageTemplate(
    businessAccountId: string,
    shopAccessToken: string,
    templateData: any,
    shopId: string,
    branchId: string,
    templateId?: string,
  ) {
    try {
      const {
        templateName,
        language,
        headerImageExample,
        bodyText,
        bodyTextExample,
        footerText,
        buttonsdata,
      } = templateData;
      const bodyTextExam = [];
      const ImageTextExam = [];
      ImageTextExam.push(headerImageExample);

      const body = {
        type: 'BODY',
        text: bodyText,
      };
      if (bodyTextExample.length > 0) {
        bodyTextExam.push(bodyTextExample);
        body['example'] = {
          body_text: bodyTextExam,
        };
      }
      const components: any = [
        {
          type: 'HEADER',
          format: 'IMAGE',
          example: {
            header_handle: ImageTextExam,
          },
        },
      ];
      components.push(body);
      if (String(footerText).trim() !== '') {
        components.push({
          type: 'FOOTER',
          text: footerText,
        });
      }

      if (buttonsdata.length > 0) {
        components.push({
          type: 'BUTTONS',
          buttons: buttonsdata,
        });
      }
      const templateBody = {
        name: templateName,
        language: language,
        category: 'MARKETING',
        components,
      };
      const config = {
        method: 'post',
        url: `https://graph.facebook.com/v18.0/${businessAccountId}/message_templates${
          templateId ? '/' + templateId : ''
        }`,
        headers: {
          Authorization: `Bearer ${shopAccessToken}`,
          'Content-Type': 'application/json',
        },
        data: templateBody,
      };

      const res = await axios(config);
      if (res?.data) {
        const tempId = res.data.id;
        this.updateTemplateMapping(shopId, branchId, tempId);
      }
      return res?.data;
    } catch (err) {
      return err.response.data; // Assuming you want to return the error response data
    }
  }

  async createEditDocMessageTemplate(
    businessAccountId: string,
    shopAccessToken: string,
    templateData: any,
    shopId: string,
    branchId: string,
    templateId?: string,
  ) {
    try {
      const {
        templateName,
        language,
        headerDocumentExample,
        bodyText,
        bodyTextExample,
        footerText,
        buttonsdata,
      } = templateData;
      const bodyTextExam = [];
      const DocTextExam = [];
      DocTextExam.push(headerDocumentExample);

      const body = {
        type: 'BODY',
        text: bodyText,
      };
      if (bodyTextExample.length > 0) {
        bodyTextExam.push(bodyTextExample);
        body['example'] = {
          body_text: bodyTextExam,
        };
      }
      const components: any = [
        {
          type: 'HEADER',
          format: 'DOCUMENT',
          example: {
            header_handle: DocTextExam,
          },
        },
      ];
      components.push(body);
      if (footerText.trim() !== '') {
        components.push({
          type: 'FOOTER',
          text: footerText,
        });
      }
      if (buttonsdata.length > 0) {
        components.push({
          type: 'BUTTONS',
          buttons: buttonsdata,
        });
      }
      const templateBody = {
        name: templateName,
        language: language,
        category: 'MARKETING',
        components,
      };
      const config = {
        method: 'post',
        url: `https://graph.facebook.com/v18.0/${businessAccountId}/message_templates${
          templateId ? '/' + templateId : ''
        }`,
        headers: {
          Authorization: `Bearer ${shopAccessToken}`,
          'Content-Type': 'application/json',
        },
        data: templateBody,
      };

      const res = await axios(config);
      if (res?.data) {
        const tempId = res.data.id;
        this.updateTemplateMapping(shopId, branchId, tempId);
      }
      return res?.data;
    } catch (err) {
      return err.response.data; // Assuming you want to return the error response data
    }
  }

  async deleteMessageTemplate(
    businessAccountId: string,
    shopAccessToken: string,
    templateId: string,
    templateName: string,
    shopId: string,
  ) {
    try {
      const config = {
        method: 'delete',
        url: `https://graph.facebook.com/v18.0/${businessAccountId}/message_templates`,
        headers: {
          Authorization: `Bearer ${shopAccessToken}`,
        },
        params: {
          hsm_id: templateId,
          name: templateName,
        },
      };

      const res = await axios(config);
      if (res?.data) {
        const tempId = res.data.id;
        const sqlQuery = ` 
        DELETE FROM    
          public."WATemplateIdBranchMapping"
        WHERE fk_shop_id='${shopId}' 
        AND wa_template_id='${tempId}';
        `;

        await this.databaseService.query(sqlQuery);
      }
      return res?.data;
    } catch (err) {
      return err.response.data; // Assuming you want to return the error response data
    }
  }

  async initiateUpload(
    appId: string,
    fileLength: number,
    fileType: string,
    accessToken: string,
    fileData?: Buffer,
  ) {
    try {
      const config = {
        method: 'post',
        url: `https://graph.facebook.com/v18.0/${appId}/uploads?file_length=${fileLength}&file_type=${fileType}`,
        headers: {
          Authorization: `Bearer ${accessToken}`,
          'Content-Type': 'application/json',
        },
        data: {},
      };

      const response = await axios(config);

      if (response) {
        const uploadId = response?.data?.id;
        const responseUpload = await axios.post(
          `https://graph.facebook.com/v18.0/${uploadId}`,
          { file: fileData },
          {
            headers: {
              Authorization: `OAuth ${accessToken}`,
              file_offset: 0,
            },
          },
        );

        return responseUpload.data?.h;
      } else {
        return false;
      }
    } catch (err) {
      throw new BadRequestException(err?.response);
    }
  }
  async createAudience(
    shopId: string,
    branchId: string,
    audiencePayload: { audienceType: string; audienceData: any[] },
  ) {
    const { audienceType, audienceData } = audiencePayload;

    const CheckExistence = `
    SELECT * FROM 
          public."AudienceTypes" 
      WHERE name='${audienceType}' AND fk_shop_id ='${shopId}'
      AND fk_branch_id ='${branchId}';`;
    const allUserInfo = audienceData.map(
      (each) => `('${each.name}','${each.phoneNumber}')`,
    );
    const usersQuery = `DROP  TABLE IF EXISTS temp_audience ;
      CREATE TEMP TABLE temp_audience (
        audience_id UUID,
        name VARCHAR(255),
        phone_number VARCHAR(30),
        audience_type_id UUID
      );
       
       INSERT INTO temp_audience (audience_id, name, phone_number)
      WITH unique_phone_numbers AS (
        SELECT DISTINCT ON (phone_number) phone_number, name
        FROM (
          VALUES ${allUserInfo.join(',')}
        ) AS t(name, phone_number)
      )
      SELECT gen_random_uuid(), name, phone_number
      FROM unique_phone_numbers;
              
      UPDATE temp_audience t
        SET audience_type_id = ty.audience_type_id
      FROM temp_uuid
        JOIN public."AudienceTypes"  ty
      ON name='${audienceType}' AND fk_shop_id ='${shopId}'
      AND fk_branch_id ='${branchId}';
`;

    const sqlQuery = `
        
        DROP TABLE IF EXISTS temp_uuid;
        CREATE TEMP TABLE temp_uuid AS
        SELECT gen_random_uuid()  AS audience_id;

        INSERT INTO public."AudienceTypes"(audience_type_id, name, fk_shop_id,fk_branch_id)
          SELECT audience_id , '${audienceType}','${shopId}','${branchId}' FROM temp_uuid
          WHERE NOT EXISTS(
            SELECT * FROM 
              public."AudienceTypes" 
            WHERE name='${audienceType}' AND fk_shop_id ='${shopId}'
            AND fk_branch_id ='${branchId}'
          )  ;
      
        ${usersQuery}

        INSERT INTO public."Audiences"(
          audience_id, name, phone_number)
          SELECT DISTINCT  audience_id,name, phone_number FROM temp_audience t
          WHERE NOT EXISTS(
            SELECT * FROM  public."Audiences" aud
            WHERE COALESCE(aud.phone_number, '') = COALESCE(t.phone_number, '') 
          );  

        INSERT INTO public."AudienceTypeMapping"(
          audience_mapping_id, fk_audience_type_id, fk_audience_id)
          SELECT gen_random_uuid(), audience_type_id,aud.audience_id
        FROM temp_audience t
          JOIN  public."Audiences" aud
            ON aud.phone_number =t.phone_number
          WHERE NOT EXISTS(
            SELECT 1 FROM  public."AudienceTypeMapping" audTy
            WHERE audTy.fk_audience_id =aud.audience_id AND audTy.fk_audience_type_id =t.audience_type_id
          ) ;

        INSERT INTO  public."AudienceShopMapping"(
          audience_shop_id, fk_shop_id, fk_branch_id,fk_audience_id, broadcast,audience_name,fk_audience_type_id)
        SELECT gen_random_uuid(), '${shopId}','${branchId}',CASE WHEN aud.audience_id IS NULL THEN t.audience_id ELSE aud.audience_id END  ,true 
            ,t.name  ,t.audience_type_id 
           FROM temp_audience t
          JOIN  public."Audiences" aud
             ON  aud.phone_number =t.phone_number
            WHERE NOT EXISTS(
              SELECT 1 FROM  public."AudienceShopMapping" audTy
                WHERE audTy.fk_audience_id =aud.audience_id AND audTy.fk_shop_id = '${shopId}'
                AND fk_branch_id ='${branchId}'
            ) ;
    `;
    try {
      const exists = await this.databaseService.query(CheckExistence);
      if (exists.length > 0) {
        throw new BadRequestException('Audience type already exists.');
      } else {
        await this.databaseService.query(sqlQuery);
        return { message: 'Audience added Sucessfully.' };
      }
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  async updateAudience(
    shopId: string,
    audiencePayload: { audienceTypes: string[]; audienceData: any[] },
  ) {
    const { audienceTypes, audienceData } = audiencePayload;

    const allUserInfo = audienceData.map(
      (each) => `('${each.name}','${each.phoneNumber}')`,
    );
    const usersQuery = `
      DROP  TABLE IF EXISTS temp_audience_update ;
      CREATE TEMP TABLE temp_audience_update (
        audience_id UUID,
        name VARCHAR(255),
        phone_number VARCHAR(30),
        branchid VARCHAR(30),
        audience_type_id UUID
      );
      INSERT INTO temp_audience_update (audience_id, name, phone_number)
      WITH unique_phone_numbers AS (
        SELECT DISTINCT ON (phone_number) phone_number, name
        FROM (
          VALUES ${allUserInfo.join(',')}
        ) AS t(name, phone_number)
      )
      SELECT gen_random_uuid(), name, phone_number
      FROM unique_phone_numbers;       
`;

    const getAudeinceQuery = (e) => `
    DROP TABLE IF EXISTS temp_uuid;
    CREATE TEMP TABLE temp_uuid AS
    SELECT  CAST('${e}' AS uuid)  AS audience_id;

    
        UPDATE temp_audience_update t
        SET audience_type_id = ty.audience_type_id,
        branchid =fk_branch_id
      FROM temp_uuid
        JOIN public."AudienceTypes"  ty
      ON ty.audience_type_id='${e}' AND fk_shop_id ='${shopId}';

      INSERT INTO public."AudienceTypeMapping"(
        audience_mapping_id, fk_audience_type_id, fk_audience_id)
        SELECT gen_random_uuid(), audience_type_id,aud.audience_id
      FROM temp_audience_update t
        JOIN  public."Audiences" aud
          ON aud.phone_number =t.phone_number
        WHERE NOT EXISTS(
          SELECT 1 FROM  public."AudienceTypeMapping" audTy
          WHERE audTy.fk_audience_id =aud.audience_id AND audTy.fk_audience_type_id =t.audience_type_id
        ) ;
      
      `;

    const sqlQuery = ` 
        
      
        ${usersQuery}

        INSERT INTO public."Audiences"(
          audience_id, name, phone_number)
          SELECT DISTINCT  audience_id,name, phone_number FROM temp_audience_update t
          WHERE NOT EXISTS(
            SELECT * FROM  public."Audiences" aud
            WHERE COALESCE(aud.phone_number, '') = COALESCE(t.phone_number, '') 
          ); 

        ${audienceTypes.map((e) => getAudeinceQuery(e)).join(' ')} 

        INSERT INTO  public."AudienceShopMapping"(
          audience_shop_id, fk_shop_id,fk_branch_id, fk_audience_id, broadcast,audience_name,fk_audience_type_id)
        SELECT gen_random_uuid(), '${shopId}',branchid,CASE WHEN aud.audience_id IS NULL THEN t.audience_id ELSE aud.audience_id END  ,true 
          ,t.name,t.audience_type_id
        FROM temp_audience_update t
          JOIN  public."Audiences" aud
              ON  aud.phone_number =t.phone_number
            WHERE NOT EXISTS(
              SELECT 1 FROM  public."AudienceShopMapping" audTy
                WHERE audTy.fk_audience_id =aud.audience_id AND audTy.fk_shop_id = '${shopId}'
               
            ) ;
    `;
    try {
      await this.databaseService.query(sqlQuery);
      return { message: 'New Audience data updated successfully.' };
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }
  async getAudiencesData(
    shopId: string,
    type: string,
    branchIdValue: string,
    audienceTypeId?: string,
    filter?: string,
  ) {
    let sqlQuery = '';
    let branchId = branchIdValue;

    const branchesQuery = `  
      SELECT branch_id
      FROM public."ShopBranches"
      WHERE fk_shop_id = $1
    `;
    const exists = await this.databaseService.query(branchesQuery, [shopId]);
    if (exists.length === 1) {
      branchId = null;
    }
    if (type === 'getAudienceNameAndCount' && filter !== 'all') {
      if (filter === 'audiences') {
        sqlQuery = `
          SELECT DISTINCT
              CAST(at.audience_type_id AS text) AS audienceTypeId,
              at.name AS audienceTypeName,
              Case WHEN at.fk_branch_id='null' THEN null else at.fk_branch_id END branchId,
              COUNT(atm.fk_audience_id) AS audienceCount,
              CASE WHEN branch_name='null' THEN null ELSE branch_name END AS branch_name,
              3 orderValue,
              true enableDeleteList,
              true enableDeleteMember
          FROM
              public."AudienceTypes" at
          LEFT JOIN
              public."AudienceTypeMapping" atm ON atm.fk_audience_type_id = at.audience_type_id
           LEFT  JOIN public."ShopBranches" br ON at.fk_shop_id = br.fk_shop_id
			 	    AND br.branch_id = at.fk_branch_id
          WHERE
              at.fk_shop_id = '${shopId}'
              AND at.fk_club_id is null`;

        if (branchId) {
          sqlQuery += ` AND at.fk_branch_id ='${branchId}'`;
        }

        sqlQuery += `
          GROUP BY
              at.audience_type_id, at.name,at.fk_branch_id,branch_name`;
      } else if (filter === 'customers') {
        sqlQuery += `
          SELECT
              CASE
                  WHEN total_bookings = 'Active Customers' THEN CONCAT(branchId, '$$active-customers')
                  ELSE CONCAT(branchId, '$$potential-customers')
              END AS audienceTypeId,
              CONCAT( total_bookings) AS audienceTypeName,
              CASE WHEN branchId ='null' THEN null ELSE branchId END branchId,
              COUNT(*) AS audienceCount,
              CASE WHEN branch_name ='null' THEN null ELSE branch_name END AS branch_name,
               1 orderValue, 
                false enableDeleteList,
              false enableDeleteMember
          FROM (
              
                SELECT DISTINCT
        c.phone_number,
        CASE
            WHEN b.fk_customer_id IS NOT NULL THEN 'Active Customers'
            ELSE 'Potential Customers'
        END AS total_bookings,
        b.fk_branch_id branchId,
        branch_name
    FROM
        public."Customers" c
    JOIN
        public."Customer_Shops" cc ON c.customer_id = cc.fk_customer_id
    
        LEFT JOIN
        public."ShopOrders" b ON c.customer_id = b.fk_customer_id AND b.fk_branch_id IN (
            SELECT branch_id
            FROM public."ShopBranches"
            WHERE fk_shop_id = '${shopId}'
        ) AND cc.fk_shop_id= b.fk_shop_id
        
   LEFT  JOIN public."ShopBranches" br ON br.fk_shop_id = cc.fk_shop_id
   AND br.branch_id =b.fk_branch_id
			 	    
    WHERE
        cc.fk_shop_id = '${shopId}' AND cc.broadcast = 'true'`;

        if (branchId) {
          sqlQuery += ` AND b.fk_branch_id ='${branchId}'`;
        }

        sqlQuery += `
          ) t
          GROUP BY
              total_bookings, branchId,branch_name`;

        // get opencart customers
        sqlQuery += `
      UNION
      SELECT 
          CONCAT(branchId, '$$open-cart-customers')  AS audienceTypeId,
          total_bookings AS audienceTypeName,
         CASE WHEN branchId='null' THEN null ELSE branchId END branchId,
          COUNT(*) AS audienceCount,
         CASE WHEN branch_name='null' THEN null ELSE branch_name END AS branch_name,
          2 orderValue, false enableDeleteList,
              false enableDeleteMember
      FROM (
          SELECT DISTINCT
              c.phone_number,
              CASE
                  WHEN ca.type = 'OPEN_CART' THEN 'Open Cart Customers'
                  ELSE ''
              END AS total_bookings,
              br.branch_id AS branchId,
              br.branch_name
          FROM public."Customers" c
          JOIN public."Customer_Shops" cc 
              ON c.customer_id = cc.fk_customer_id
          JOIN public."ShopBranches" br 
              ON br.fk_shop_id = cc.fk_shop_id
          JOIN public."CustomerAnalytics" ca 
              ON ca.fk_branch_id = br.branch_id
              AND ca.fk_shop_id = cc.fk_shop_id
                AND ca.fk_customer_id= c.customer_id 
                  WHERE cc.fk_shop_id = '${shopId}' 
              AND cc.broadcast = 'true' AND ca.type = 'OPEN_CART'
              ${branchId ? `AND br.branch_id = '${branchId}'` : ''}
      ) AS derived_table
      GROUP BY 
          total_bookings, 
          branchId,
          branch_name`;
      }
    } else if (filter === 'tags') {
      sqlQuery += `
        SELECT 
          CONCAT('SUBTAG$$', mst.tag_id) audienceTypeId,
          CONCAT(CASE WHEN mst.is_facility=true THEN 'Facility:' ELSE 'Tag:' END, mst.tag_name) audienceTypeName,
          CASE WHEN ctm.fk_branch_id='null' THEN null ELSE ctm.fk_branch_id END fk_branch_id,
          COUNT(ctm.fk_sub_tag_id) audienceCount,
          CASE WHEN branch_name='null' THEN null ELSE branch_name END AS branch_name,
          4 orderValue, 
           false enableDeleteList,
              true enableDeleteMember
        FROM public."CustomerTagsMapping" ctm
        JOIN public."MasterSubTags" mst ON mst.tag_id = ctm.fk_sub_tag_id
        LEFT JOIN public."ShopBranches" br ON br.fk_shop_id = ctm.fk_shop_id
          AND br.branch_id = ctm.fk_branch_id     
        WHERE mst.fk_shop_id = '${shopId}'
          AND mst.is_facility= false AND ctm.fk_shop_id = '${shopId}'
      `;
      if (branchId) {
        sqlQuery += ` AND ctm.fk_branch_id ='${branchId}'`;
      }

      sqlQuery += `
          GROUP BY ctm.fk_sub_tag_id, tag_name, is_facility, ctm.fk_branch_id, branch_name`;

      sqlQuery += `
          ORDER BY
              orderValue;`;
    } else if (
      (type === 'getAudienceNameAndCount' && (!filter || filter === 'all')) ||
      type === 'getBroadcastData'
    ) {
      sqlQuery = `
          SELECT DISTINCT
              CAST(at.audience_type_id AS text) AS audienceTypeId,
              at.name AS audienceTypeName,
            CASE WHEN  at.fk_branch_id='null' THEN null ELSE at.fk_branch_id END branchId,
              COUNT(atm.fk_audience_id) AS audienceCount,
           CASE WHEN   branch_name='null' THEN null ELSE branch_name END AS branch_name,
              3 orderValue,
               true enableDeleteList,
              true enableDeleteMember 
          FROM
              public."AudienceTypes" at
          LEFT JOIN
              public."AudienceTypeMapping" atm ON atm.fk_audience_type_id = at.audience_type_id
           LEFT  JOIN public."ShopBranches" br ON at.fk_shop_id = br.fk_shop_id
			 	    AND br.branch_id = at.fk_branch_id
          WHERE
              at.fk_shop_id = '${shopId}'
              AND at.fk_club_id is null`;

      if (branchId) {
        sqlQuery += ` AND at.fk_branch_id ='${branchId}'`;
      }

      sqlQuery += `
          GROUP BY
              at.audience_type_id, at.name,at.fk_branch_id,branch_name
          UNION
          SELECT
              CASE
                  WHEN total_bookings = 'Active Customers' THEN CONCAT(branchId, '$$active-customers')
                  ELSE CONCAT(branchId, '$$potential-customers')
              END AS audienceTypeId,
              CONCAT( total_bookings) AS audienceTypeName,
              branchId branchId,
              COUNT(*) AS audienceCount,
               branch_name,
               1,false,false
          FROM (
              
                SELECT DISTINCT
        c.phone_number,
        CASE
            WHEN b.fk_customer_id IS NOT NULL THEN 'Active Customers'
            ELSE 'Potential Customers'
        END AS total_bookings,
        b.fk_branch_id branchId,
        branch_name
    FROM
        public."Customers" c
    JOIN
        public."Customer_Shops" cc ON c.customer_id = cc.fk_customer_id
    
        LEFT JOIN
        public."ShopOrders" b ON c.customer_id = b.fk_customer_id AND b.fk_branch_id IN (
            SELECT branch_id
            FROM public."ShopBranches"
            WHERE fk_shop_id = '${shopId}'
        ) AND cc.fk_shop_id= b.fk_shop_id
        
   LEFT  JOIN public."ShopBranches" br ON br.fk_shop_id = cc.fk_shop_id
   AND br.branch_id =b.fk_branch_id
			 	    
    WHERE
        cc.fk_shop_id = '${shopId}' AND cc.broadcast = 'true'`;

      if (branchId) {
        sqlQuery += ` AND b.fk_branch_id ='${branchId}'`;
      }

      sqlQuery += `
          ) t
          GROUP BY
              total_bookings, branchId,branch_name`;

      // get opencart customers
      sqlQuery += `
      UNION
      SELECT 
          CONCAT(branchId, '$$open-cart-customers')  AS audienceTypeId,
          total_bookings AS audienceTypeName,
          branchId,
          COUNT(*) AS audienceCount,
          branch_name,
          2,false,false
      FROM (
          SELECT DISTINCT
              c.phone_number,
              CASE
                  WHEN ca.type = 'OPEN_CART' THEN 'Open Cart Customers'
                  ELSE ''
              END AS total_bookings,
              br.branch_id AS branchId,
              br.branch_name
          FROM public."Customers" c
          JOIN public."Customer_Shops" cc 
              ON c.customer_id = cc.fk_customer_id
          JOIN public."ShopBranches" br 
              ON br.fk_shop_id = cc.fk_shop_id
          JOIN public."CustomerAnalytics" ca 
              ON ca.fk_branch_id = br.branch_id
              AND ca.fk_shop_id = cc.fk_shop_id
                AND ca.fk_customer_id= c.customer_id 
          WHERE cc.fk_shop_id = '${shopId}' 
              AND cc.broadcast = 'true' AND ca.type = 'OPEN_CART'
              ${branchId ? `AND br.branch_id = '${branchId}'` : ''}
      ) AS derived_table
      GROUP BY 
          total_bookings, 
          branchId,
          branch_name`;
      sqlQuery += `
          UNION
          SELECT 
          CONCAT('SUBTAG$$', mst.tag_id) audienceTypeId,
          CONCAT('Tag:', mst.tag_name) audienceTypeName,
            mst.fk_branch_id,
          COUNT(ctm.fk_sub_tag_id) audienceCount,
          branch_name,
          4 orderValue, 
            false enableDeleteList,
              true enableDeleteMember 
        FROM public."MasterSubTags" mst
        LEFT JOIN public."CustomerTagsMapping" ctm 
            ON mst.tag_id = ctm.fk_sub_tag_id 
             AND mst.fk_shop_id = ctm.fk_shop_id
        LEFT JOIN public."ShopBranches" br ON br.fk_shop_id = mst.fk_shop_id
          AND br.branch_id = mst.fk_branch_id     
        JOIN public."MasterSuperTags" sst 
            ON mst.fk_super_tag_id = sst.tag_id
        WHERE mst.fk_shop_id = '${shopId}'
          AND mst.is_facility= false        `;

      if (branchId) {
        sqlQuery += ` AND mst.fk_branch_id ='${branchId}'`;
      }

      sqlQuery += `
                    GROUP BY mst.tag_id, mst.tag_name,   mst.fk_branch_id, br.branch_name`;

      sqlQuery += `
          ORDER BY
              orderValue;`;
    } else if (type === 'getAudienceBasedOnType') {
      if (audienceTypeId.includes('$$active-customers')) {
        const branchIds = audienceTypeId?.split('$$')[0];
        sqlQuery = `
        SELECT DISTINCT
                 c.phone_number audienceId,
                 COALESCE(cc.customer_name,c.customer_name) audienceName,
                  c.phone_number audiencePhone
            FROM
                public."Customers" c
            JOIN public."Customer_Shops" cc ON c.customer_id = cc.fk_customer_id
             AND  '${branchIds}'= ANY(branch_ids) 
            JOIN public."ShopOrders" b ON c.customer_id = b.fk_customer_id AND b.fk_branch_id = '${branchIds}'
            WHERE
                cc.fk_shop_id = '${shopId}'   AND cc.broadcast = 'true'
                AND b.fk_customer_id IS NOT NULL;`;
      } else if (audienceTypeId.includes('$$open-cart-customers')) {
        const branchIds = audienceTypeId?.split('$$')[0];
        sqlQuery = `
        SELECT DISTINCT
              c.phone_number AS audienceId,
              COALESCE(cc.customer_name, c.customer_name) AS audienceName,
              c.phone_number AS audiencePhone
          FROM 
              public."Customers" c
          JOIN 
              public."Customer_Shops" cc 
              ON c.customer_id = cc.fk_customer_id
          JOIN 
              public."CustomerAnalytics" ca 
              ON ca.fk_customer_id = c.customer_id
              AND ca.fk_shop_id = cc.fk_shop_id
          WHERE 
              cc.fk_shop_id = '${shopId}'
              AND cc.broadcast = 'true'
              AND ca.type = 'OPEN_CART'
              AND ca.fk_branch_id = '${branchIds}'`;
      } else if (audienceTypeId.includes('$$potential-customers')) {
        const branchIds = audienceTypeId?.split('$$')[0];
        sqlQuery = `
        SELECT 
          DISTINCT
                c.phone_number audienceId,
                COALESCE(cc.customer_name,c.customer_name) audienceName,
                c.phone_number audiencePhone
          FROM
              public."Customers" c
          JOIN public."Customer_Shops" cc ON c.customer_id = cc.fk_customer_id
            ${branchIds ? ` AND  '${branchIds}'= ANY(branch_ids) ` : ''}
          LEFT JOIN public."ShopOrders" b ON c.customer_id = b.fk_customer_id  
            AND  b.fk_shop_id = '${shopId}' 
           ${branchIds ? ` AND b.fk_branch_id = '${branchIds}'` : ''}
         WHERE
            cc.fk_shop_id = '${shopId}'   AND cc.broadcast = 'true'
            AND b.fk_customer_id IS NULL;`;
      } else if (audienceTypeId.includes('SUBTAG$$')) {
        const subTag = audienceTypeId.split('SUBTAG$$')[1];
        sqlQuery = `
         SELECT DISTINCT ON (COALESCE(a.phone_number, fk_customer_phone)) 
            fk_customer_phone audienceId,
            fk_customer_phone audiencePhone,
           COALESCE( COALESCE(cc.customer_name, c.customer_name),a.name) AS audienceName
            FROM public."CustomerTagsMapping" ctm
              JOIN public."MasterSubTags" mst on mst.tag_id = ctm.fk_sub_tag_id 
             LEFT JOIN public."Customers" c on c.phone_number = fk_customer_phone
             LEFT JOIN public."Customer_Shops" cc ON c.customer_id = cc.fk_customer_id AND cc.fk_shop_id=mst.fk_shop_id 
             LEFT JOIN public."Audiences" a ON a.phone_number = fk_customer_phone
              WHERE mst.fk_shop_id =  '${shopId}' 
            AND  ctm.fk_sub_tag_id IN ('${subTag}')`;
      } else {
        sqlQuery = `SELECT DISTINCT
              a.audience_id audienceId,
              COALESCE(club.audience_name,a.name) AS audienceName,
      
              a.phone_number AS audiencePhone
          FROM
              public."AudienceTypeMapping" atm
           JOIN
              public."AudienceTypes" at ON atm.fk_audience_type_id = at.audience_type_id
        
           JOIN
              public."Audiences" a ON atm.fk_audience_id = a.audience_id
          JOIN public."AudienceShopMapping" club ON club.fk_audience_id = a.audience_id
             AND  at.fk_shop_id = club.fk_shop_id
              WHERE
              at.fk_shop_id = '${shopId}'
              AND atm.fk_audience_type_id =  '${audienceTypeId}'
              `;
        if (branchId) {
          sqlQuery += ` AND at.fk_branch_id ='${branchId}'`;
        }
      }
    } else if (type === 'getAllAudience') {
      sqlQuery = `
      WITH audience_types AS (
        SELECT
          fk_audience_id,
          STRING_AGG(DISTINCT name, ',') AS audience_types,
            fk_branch_id,
		      branch_name
        FROM
          public."AudienceTypeMapping" atm
        LEFT JOIN
          public."AudienceTypes" at ON atm.fk_audience_type_id = at.audience_type_id
          LEFT  JOIN public."ShopBranches" br ON br.fk_shop_id = at.fk_shop_id
      	AND branch_id = fk_branch_id
        WHERE
          at.fk_shop_id = '${shopId}'
          ${branchId ? ` AND fk_branch_id ='${branchId}'` : ''}
        GROUP BY fk_audience_id, fk_branch_id, branch_name
      ),
      tag_types AS (
        SELECT 
          a.audience_id,
          STRING_AGG(DISTINCT CONCAT( CASE WHEN is_facility=true then 'Facility:' ELSE 'Tag:' END, mst.tag_name), ',') as tag_types
        FROM public."Audiences" a
        JOIN public."CustomerTagsMapping" ctm ON ctm.fk_customer_phone = a.phone_number
        JOIN public."MasterSubTags" mst ON mst.tag_id = ctm.fk_sub_tag_id
        WHERE mst.fk_shop_id = '${shopId}'
          ${branchId ? ` AND mst.fk_branch_id ='${branchId}'` : ''}
        GROUP BY a.audience_id
      )
      SELECT DISTINCT
        at.fk_audience_id AS audienceId,
         COALESCE(CASE 
          WHEN tt.tag_types IS NOT NULL AND aud.audience_types IS NOT NULL 
          THEN CONCAT(aud.audience_types, ', ', tt.tag_types)
          WHEN tt.tag_types IS NOT NULL THEN tt.tag_types
          ELSE aud.audience_types
        END,  '') AS audienceTypes,
        CASE WHEN broadcast='false' THEN 'No' ELSE 'Yes' END AS broadcast,
        COALESCE(at.audience_name, a.name) AS audienceName,
        a.phone_number AS audiencePhone,
        aud.fk_branch_id,
        aud.branch_name
      FROM
        public."Audiences" a
      JOIN
        public."AudienceShopMapping" at ON a.audience_id = at.fk_audience_id
        JOIN
        audience_types aud ON aud.fk_audience_id = at.fk_audience_id
      LEFT JOIN
        tag_types tt ON tt.audience_id = a.audience_id
      WHERE   at.fk_shop_id =  '${shopId}'`;

      if (branchId) {
        sqlQuery += `  AND at.fk_branch_id ='${branchId}'`;
      }
    } else if (type === 'getCustomersPotentialCustomersCount') {
      sqlQuery = `
        select total_bookings audienceTypeId,total_bookings audienceTypeName,count(*) audienceCount FROM (
          SELECT DISTINCT
                   c.phone_number,
                  CASE WHEN b.fk_customer_id IS NOT NULL THEN 'Customers' ELSE 'Potential Customers' END AS total_bookings
                  
          FROM
              public."Customers" c
          JOIN public."Customer_Shops" cc ON c.customer_id = cc.fk_customer_id
          LEFT JOIN public."ShopOrders" b ON c.customer_id = b.fk_customer_id AND b.fk_branch_id IN  (
          SELECT branch_id
            FROM public."ShopBranches"
          WHERE fk_shop_id = '${shopId}'
          ) 
        
          WHERE
              cc.fk_shop_id = '${shopId}' `;

      if (branchId) {
        sqlQuery += `  AND b.fk_branch_id   ='${branchId}'`;
      }
      sqlQuery += ` 
          AND cc.broadcast = 'true'
          ) t
          group by total_bookings
      `;
    }

    return await this.databaseService.query(sqlQuery);
  }

  async deleteAudienceType(shopId: string, audienceTypeId: string) {
    try {
      const sqlQuery = `
      DELETE FROM
      public."AudienceTypeMapping" atm  
      USING
          public."AudienceTypes" at 
      WHERE
        atm.fk_audience_type_id = at.audience_type_id
        AND at.fk_shop_id = '${shopId}'
        AND at.audience_type_id = CAST('${audienceTypeId}' AS uuid);

        DELETE FROM public."AudienceTypes"
        WHERE audience_type_id='${audienceTypeId}';
 `;
      const CheckExistence = `
      SELECT * FROM 
            public."AudienceTypes" 
        WHERE audience_type_id= CAST('${audienceTypeId}' AS uuid) AND fk_shop_id ='${shopId}'`;
      const exists = await this.databaseService.query(CheckExistence);
      if (exists.length === 0) {
        throw new BadRequestException('Invalid Audience type.');
      } else {
        return await this.databaseService.query(sqlQuery);
      }
    } catch (error) {
      throw new BadRequestException(error.error);
    }
  }

  async deleteAudience(shopId: string, audienceData: any) {
    try {
      // let audienceIdsQuery = '';
      let branchConditions = '';
      let count = 1;

      for (const each of audienceData) {
        // audienceIdsQuery += `'${each.audience_id}'`;
        branchConditions += `(CAST(fk_audience_id AS text) = '${each.audience_id}' AND fk_branch_id = '${each.branch_id}')`;

        if (count !== audienceData.length) {
          // audienceIdsQuery += ',';
          branchConditions += ' OR ';
        }
        count++;
      }

      const sqlQuery = `
    
        DELETE FROM public."AudienceShopMapping"
        WHERE fk_shop_id = '${shopId}'
        AND (${branchConditions});
    
        DELETE FROM public."AudienceTypeMapping" atm  
        USING public."AudienceTypes" at 
        WHERE atm.fk_audience_type_id = at.audience_type_id
        AND at.fk_shop_id = '${shopId}'
        AND (${branchConditions});
      `;
      return await this.databaseService.query(sqlQuery);
    } catch (error) {}
  }

  async storeBoradCastInfo(
    shopId: string,
    audiencePayload: {
      templateDetails: {
        templateName: string;
        templateLanguage: string;
        templateType: string;
        imageOrDocURL: string;
        headerParameters: string;
        bodyParameters: string;
      };
      selectedAudienceData: string[];
    },
  ) {
    const { templateDetails, selectedAudienceData } = audiencePayload;
    const {
      templateName,
      templateLanguage,
      templateType,
      imageOrDocURL,
      headerParameters,
      bodyParameters,
    } = templateDetails;

    const audienceIdsQuery = [];
    const subTags = [];
    const potential = [];
    const active = [];
    const openCart = [];
    let noAudience = true;
    for (const each of selectedAudienceData) {
      const subTagCustomersIncluded = each.includes('SUBTAG$$');
      const customersIncluded = each.includes('active-customers');
      const potentialCustomersIncluded = each.includes('potential-customers');
      const openCartCustomersIncluded = each.includes('$$open-cart-customers');

      if (subTagCustomersIncluded) {
        subTags.push("'" + each.split('SUBTAG$$')[1] + "'");
      } else if (customersIncluded) {
        const branchId = each?.split('$$')[0];
        active.push("'" + branchId + "'");
      } else if (potentialCustomersIncluded) {
        const branchId = each?.split('$$')[0];
        potential.push("'" + branchId + "'");
      } else if (openCartCustomersIncluded) {
        const branchId = each?.split('$$')[0];
        openCart.push("'" + branchId + "'");
      } else {
        audienceIdsQuery.push(`'${each}'`);
        noAudience = false;
      }
    }
    let sqlQuery = `
      DROP TABLE IF EXISTS temp_uuid2;
       DROP TABLE IF EXISTS temp_audience_broadcast;
      CREATE TEMP TABLE temp_uuid2 AS
      SELECT gen_random_uuid()  AS broadcast_msg_id;

      INSERT INTO public."BroadcastMessagesData"(
        fk_shop_id, broadcast_msg_id, template_name, template_lang, template_type, image_doc_url, header_parameters, body_parameters)
      SELECT  '${shopId}',broadcast_msg_id, '${templateName}', '${templateLanguage}', '${templateType}', '${imageOrDocURL}', '${headerParameters}', '${bodyParameters}' FROM temp_uuid2;
       
      CREATE TEMP TABLE temp_audience_broadcast (
        fk_broadcast_msg_id UUID,
        status VARCHAR(255),
        phone_number VARCHAR(30),
        user_type VARCHAR(255),
        selected_type_name VARCHAR(255)
      );
      `;
    if (audienceIdsQuery.length > 0 && !noAudience) {
      sqlQuery += `
      INSERT INTO temp_audience_broadcast(
        fk_broadcast_msg_id, phone_number, status,user_type, selected_type_name)
      SELECT DISTINCT
        broadcast_msg_id,
        a.phone_number,'Pending',
        'Audiences',
        at.name
      FROM
          public."AudienceTypeMapping" atm
          JOIN temp_uuid2 ON 1=1
      JOIN
          public."AudienceTypes" at ON atm.fk_audience_type_id = at.audience_type_id
    
      JOIN
          public."Audiences" a ON atm.fk_audience_id = a.audience_id
      WHERE
          at.fk_shop_id = '${shopId}'
          AND    CAST(atm.fk_audience_type_id AS text) IN (${audienceIdsQuery.join(',')}); 
     `;
    }

    if (subTags.length > 0) {
      sqlQuery += `
      
      INSERT INTO temp_audience_broadcast(
        fk_broadcast_msg_id, phone_number, status
        ,user_type, selected_type_name)
      SELECT DISTINCT
        broadcast_msg_id,
        fk_customer_phone,'Pending',
        'Customers',
        tag_name
        FROM public."CustomerTagsMapping" ctm
          JOIN public."MasterSubTags" mst on mst.tag_id = ctm.fk_sub_tag_id 
          JOIN temp_uuid2 ON 1=1
          WHERE mst.fk_shop_id =  '${shopId}' 
        AND  ctm.fk_sub_tag_id IN (${subTags.join(',')});
      `;
    }

    if (potential.length > 0) {
      sqlQuery += `
      INSERT INTO temp_audience_broadcast(
        fk_broadcast_msg_id, phone_number, status,user_type, selected_type_name)
        SELECT DISTINCT
          broadcast_msg_id,c.phone_number,'Pending',
          'Customers',
          'Potential Customers'
        FROM
            public."Customers" c
            JOIN temp_uuid2 ON 1=1
        JOIN public."Customer_Shops" cc ON c.customer_id = cc.fk_customer_id 
          
        `;

      sqlQuery += `
      LEFT JOIN public."ShopOrders" b ON c.customer_id = b.fk_customer_id 
         AND b.fk_shop_id = cc.fk_shop_id
         WHERE
            cc.fk_shop_id = '${shopId}' AND cc.broadcast = 'true'
            AND b.fk_customer_id IS  NULL;
       `;
    }

    if (active.length > 0) {
      sqlQuery += `
      INSERT INTO temp_audience_broadcast(
        fk_broadcast_msg_id, phone_number, status,user_type, selected_type_name)
        SELECT DISTINCT
          broadcast_msg_id,c.phone_number,'Pending',
          'Customers',
          'Active Customers'
        FROM
            public."Customers" c
            JOIN temp_uuid2 ON 1=1
        JOIN public."Customer_Shops" cc ON c.customer_id = cc.fk_customer_id 
        

        `;
      sqlQuery += `
      LEFT JOIN public."ShopOrders" b ON c.customer_id = b.fk_customer_id
        AND b.fk_shop_id = cc.fk_shop_id AND b.fk_branch_id  IN (${active.join(',')})
         WHERE
            cc.fk_shop_id = '${shopId}' AND cc.broadcast = 'true'
            AND b.fk_customer_id IS NOT NULL   AND  EXISTS (
              SELECT 1 
              FROM unnest(branch_ids) AS branch_id 
              WHERE branch_id IN (${active.join(',')})
          );
       `;
    }
    if (openCart.length > 0) {
      sqlQuery += `
      INSERT INTO temp_audience_broadcast (
        fk_broadcast_msg_id, 
        phone_number, 
        status,user_type, selected_type_name
        )
        SELECT DISTINCT 
            broadcast_msg_id, 
            c.phone_number, 
            'Pending',
            'Customers',
            'Open Cart Customers'
        FROM 
            public."Customers" c
        JOIN temp_uuid2 ON 1=1
        JOIN 
            public."Customer_Shops" cc 
            ON c.customer_id = cc.fk_customer_id
        JOIN 
            public."CustomerAnalytics" ca 
            ON ca.fk_customer_id = c.customer_id
            AND ca.fk_shop_id = cc.fk_shop_id
        WHERE 
            cc.fk_shop_id = '${shopId}'
            AND cc.broadcast = true
            AND ca.type = 'OPEN_CART'
            AND ca.fk_customer_id IS NOT NULL
            AND fk_branch_id IN (${openCart.join(',')});`;
    }
    sqlQuery += ` WITH deduplicated AS (
        SELECT 
          phone_number,
          first_value(fk_broadcast_msg_id) OVER (PARTITION BY phone_number ORDER BY user_type) as fk_broadcast_msg_id,
          'done' as status,
          first_value(user_type) OVER (PARTITION BY phone_number ORDER BY user_type) as user_type,
          selected_type_name
        FROM temp_audience_broadcast tm
          WHERE NOT EXISTS(
          SELECT 1 FROM public."Customers" c
            JOIN public."Customer_Shops" cc ON c.customer_id = cc.fk_customer_id
            WHERE c.phone_number = tm.phone_number
              AND cc.fk_shop_id = '${shopId}'
              AND cc.broadcast <> true
              AND c.phone_number IS NOT NULL
          )
      )
      INSERT INTO public."BroadcastMessageAudience"(
              fk_broadcast_msg_id, phone_number, status,user_type, selected_type_name)
      SELECT 
          fk_broadcast_msg_id,
          phone_number,
          'Pending',
          user_type,
          array_agg(selected_type_name) as selected_type_names
      FROM deduplicated
      GROUP BY fk_broadcast_msg_id, phone_number, status, user_type
      ORDER BY phone_number;
    `;
    try {
      await this.databaseService.query(sqlQuery);
      return { message: 'Broadcast Queued Sucessfully.' };
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  async getTemplateAnalytics(
    shopInfo: {
      businessAccountId: string;
      shopAccessToken: string;
    },
    startTime: string | number,
    endTime: string | number,
    templateId: string,
  ) {
    const { businessAccountId, shopAccessToken } = shopInfo;
    try {
      const config = {
        method: 'get',
        url: `https://graph.facebook.com/v18.0/${businessAccountId}/template_analytics?start=${startTime}&end=${endTime}&granularity=DAILY&phone_numbers([])&template_ids=['${templateId}']&limit=100`,
        headers: {
          Authorization: `Bearer ${shopAccessToken}`,
          'Content-Type': 'application/json',
        },
      };

      const res = await axios(config);
      return res?.data;
    } catch (err) {
      throw new BadRequestException(err.response.data.error.error_data);
    }
  }
  async getCummulativeTemplateAnalytics(
    templateData: any,
    donotCalcCum: boolean,
  ) {
    try {
      let totalSent = 0;
      let totalDelivered = 0;
      let totalRead = 0;
      const clickedButtonCountsByName = {};
      templateData?.data[0]?.data_points.forEach((dataPoint) => {
        // Update the sum values
        totalSent += donotCalcCum ? 0 : dataPoint.sent;
        totalDelivered += donotCalcCum ? 0 : dataPoint.delivered;
        totalRead += donotCalcCum ? 0 : dataPoint.read;
        dataPoint?.clicked?.forEach((clickData) => {
          if (
            clickData?.type === 'unique_url_button' ||
            clickData?.type === 'quick_reply_button'
          ) {
            const buttonType = clickData.button_content;

            // Update count for the button type
            if (clickedButtonCountsByName[buttonType]) {
              clickedButtonCountsByName[buttonType] += donotCalcCum
                ? 0
                : clickData.count;
            } else {
              clickedButtonCountsByName[buttonType] = donotCalcCum
                ? 0
                : clickData.count;
            }
          }
        });
      });

      return {
        totalSent,
        totalDelivered,
        totalRead,
        clickedButtonCountsByName,
      };
    } catch (err) {
      return err.response.data; // Assuming you want to return the error response data
    }
  }
  async calculateNewEpochStartEnd(startTime, endTime) {
    // 1. Epoch Time Difference and Period Calculation
    const timeDifference = endTime - startTime;
    const daysDifference = Math.floor(timeDifference / EPOCH_MS_PER_DAY);
    let comparisonDays = 7; // Default
    if (daysDifference >= 90) {
      comparisonDays = 90;
    } else if (daysDifference >= 60) {
      comparisonDays = 60;
    } else if (daysDifference >= 30) {
      comparisonDays = 30;
    } else if (daysDifference >= 15) {
      comparisonDays = 15;
    }

    const comparisonStartTime = startTime - comparisonDays * EPOCH_MS_PER_DAY;

    return {
      newStartTime: comparisonStartTime,
      newEndTime: startTime,
      daysDifference: comparisonDays,
    };
  }
  calculateImprovements(currentData, comparisonData) {
    const cumulativeData = {
      totalSent: 0,
      totalDelivered: 0,
      totalRead: 0,
      clickedButtonCountsByName: {},
      performanceChange: {
        totalSent: 0,
        totalDelivered: 0,
        totalRead: 0,
        clickedButtonCountsByName: {},
      },
    };

    // Calculate sums and improvements for simple metrics
    for (const metric of ['totalSent', 'totalDelivered', 'totalRead']) {
      cumulativeData[metric] = currentData[metric] + comparisonData[metric];
      cumulativeData.performanceChange[metric] = this.calculatePercentageChange(
        currentData[metric],
        comparisonData[metric],
      );
    }

    const results = {};

    for (const key of Object.keys(currentData?.clickedButtonCountsByName)) {
      const newValue = currentData.clickedButtonCountsByName[key] || 0;
      const oldValue = comparisonData.clickedButtonCountsByName[key] || 0;
      const change = this.calculatePercentageChange(newValue, oldValue);
      cumulativeData.clickedButtonCountsByName[key] = newValue + oldValue;
      results[key] = change; // Format with 2 decimal places
    }

    //   currentClickSum + comparisonClickSum;
    cumulativeData.performanceChange.clickedButtonCountsByName = results;

    return cumulativeData;
  }

  calculatePercentageChange(newValue, oldValue) {
    if (oldValue === 0 && newValue !== 0) return 100;
    if (oldValue === 0 && newValue === 0) return 0;
    return (((newValue - oldValue) / oldValue) * 100).toFixed(2);
  }

  calculateSum(data) {
    const buttonCounts = data;
    let sum = 0;
    for (const key in buttonCounts) {
      sum += buttonCounts[key];
    }

    return sum;
  }
  async getAllAnalytics(
    shopInfo: {
      businessAccountId: string;
      shopAccessToken: string;
    },
    startTime: string | number,
    endTime: string | number,
  ) {
    const { businessAccountId, shopAccessToken } = shopInfo;
    try {
      const config = {
        method: 'get',
        url: `https://graph.facebook.com/v18.0/${businessAccountId}?fields=analytics.start(${startTime}).end(${endTime}).granularity(DAY).phone_numbers([]).metric_types([]).product_types([ ]).message_media_types([]).interaction_types([]).country_codes([])&limit=100`,
        headers: {
          Authorization: `Bearer ${shopAccessToken}`,
          'Content-Type': 'application/json',
        },
      };

      const res = await axios(config);
      return res?.data;
    } catch (err) {
      return err.response.data; // Assuming you want to return the error response data
    }
  }
  async getCummulativeAllAnalytics(templateData: any, donotCalcCum: boolean) {
    try {
      let totalSent = 0;
      let totalDelivered = 0;
      templateData?.analytics?.data_points.forEach((dataPoint) => {
        totalSent += donotCalcCum ? 0 : dataPoint.sent;
        totalDelivered += donotCalcCum ? 0 : dataPoint.delivered;
      });
      return {
        totalSent,
        totalDelivered,
      };
    } catch (err) {
      return err.response.data; // Assuming you want to return the error response data
    }
  }
  calculateAllImprovements(currentData, comparisonData) {
    const cumulativeData = {
      totalSent: 0,
      totalDelivered: 0,
      performanceChange: {
        totalSent: 0,
        totalDelivered: 0,
      },
    };

    // Calculate sums and improvements for simple metrics
    for (const metric of ['totalSent', 'totalDelivered']) {
      cumulativeData[metric] = currentData[metric] + comparisonData[metric];
      cumulativeData.performanceChange[metric] = this.calculatePercentageChange(
        currentData[metric],
        comparisonData[metric],
      );
    }

    return cumulativeData;
  }
  async getAllConversationAnalytics(
    shopInfo: {
      businessAccountId: string;
      shopAccessToken: string;
    },
    startTime: string | number,
    endTime: string | number,
  ) {
    const { businessAccountId, shopAccessToken } = shopInfo;
    try {
      const config = {
        method: 'get',
        url: `https://graph.facebook.com/v18.0/${businessAccountId}?fields=conversation_analytics.start(${startTime}).end(${endTime}).granularity(DAILY).phone_numbers([]).country_codes([]).metric_types([]).conversation_types([]).conversation_directions([]).conversation_categories([]).dimensions(["CONVERSATION_CATEGORY","CONVERSATION_DIRECTION","CONVERSATION_TYPE","COUNTRY","PHONE"])&limit=100000`,
        headers: {
          Authorization: `Bearer ${shopAccessToken}`,
          'Content-Type': 'application/json',
        },
      };

      const res = await axios(config);
      return res?.data;
    } catch (err) {
      return err.response.data; // Assuming you want to return the error response data
    }
  }
  async getCummulativeAllConversationAnalytics(
    data: any,
    donotCalcCum: boolean,
  ) {
    try {
      const templateData = data.conversation_analytics.data[0].data_points;

      const cumulativeData = {
        AUTHENTICATION: { totalCost: 0, totalConversations: 0 },
        MARKETING: { totalCost: 0, totalConversations: 0 },
        SERVICE: { totalCost: 0, totalConversations: 0 },
        UTILITY: { totalCost: 0, totalConversations: 0 },
      };

      for (const dataPoint of templateData) {
        const category = dataPoint.conversation_category;
        cumulativeData[category].totalCost += donotCalcCum ? 0 : dataPoint.cost;
        cumulativeData[category].totalConversations += donotCalcCum
          ? 0
          : dataPoint.conversation;
      }

      return cumulativeData;
    } catch (err) {
      return err; // Assuming you want to return the error response data
    }
  }

  calculateConversationImprovements(data1, data2) {
    const combinedData = {
      AUTHENTICATION: {
        totalCost: 0,
        totalConversations: 0,
        costPercentage: 0,
        conversationsPercentage: 0,
      },
      MARKETING: {
        totalCost: 0,
        totalConversations: 0,
        costPercentage: 0,
        conversationsPercentage: 0,
      },
      SERVICE: {
        totalCost: 0,
        totalConversations: 0,
        costPercentage: 0,
        conversationsPercentage: 0,
      },
      UTILITY: {
        totalCost: 0,
        totalConversations: 0,
        costPercentage: 0,
        conversationsPercentage: 0,
      },
    };

    // Iterate through all keys, combining data
    for (const key in data1) {
      combinedData[key] = {
        totalCost: data1[key].totalCost + data2[key].totalCost,
        totalConversations:
          data1[key].totalConversations + data2[key].totalConversations,
      };
    }

    // Calculate percentages
    const totalCost = Object.values(combinedData).reduce(
      (sum, item) => sum + (item?.totalCost || 0),
      0,
    );
    const totalConversations = Object.values(combinedData).reduce(
      (sum, item) => sum + (item.totalConversations || 0),
      0,
    );

    for (const key in combinedData) {
      combinedData[key].costPercentage = parseFloat(
        ((combinedData[key].totalCost / (totalCost || 1)) * 100).toFixed(2),
      );
      combinedData[key].conversationsPercentage = parseFloat(
        (
          (combinedData[key].totalConversations / (totalConversations || 1)) *
          100
        ).toFixed(2),
      );
    }

    return combinedData;
  }
  async updateTemplateMapping(
    shopId: string,
    branchId: string,
    tempId: string,
  ): Promise<void> {
    const sqlQuery = `
      DELETE FROM public."WATemplateIdBranchMapping"
      WHERE fk_shop_id='${shopId}' AND fk_branch_id='${branchId}'
      AND wa_template_id='${tempId}';
      
      INSERT INTO public."WATemplateIdBranchMapping"(
        fk_shop_id, fk_branch_id, wa_template_id
      ) VALUES ('${shopId}', '${branchId}', '${tempId}');
    `;

    try {
      await this.databaseService.query(sqlQuery);
    } catch (error) {
      // Handle error appropriately, e.g., log it or throw a custom exception
      throw new Error('Failed to update template mapping');
    }
  }

  async createFlow(
    shopId: string,
    businessAccountId: string,
    shopAccessToken: string,
  ) {
    try {
      const config = {
        method: 'post',
        url: `https://graph.facebook.com/v18.0/${businessAccountId}/flows`,
        headers: {
          Authorization: `Bearer ${shopAccessToken}`,
          'Content-Type': 'application/json',
        },

        data: {
          name: 'ADD_ADDRESS',
          categories: ['OTHER'],
        },
      };

      const res = await axios(config);
      if (res) {
        const flowId = res.data?.id; // ****************;
        const sqlQuery = `
        UPDATE public."Shops"
        set wa_address_form_id= '${flowId}'  
        WHERE shop_id='${shopId}'  
      `;
        this.databaseService.query(sqlQuery);

        const formData = new FormData();
        const addAddressForm = {
          version: '3.1',
          screens: [
            {
              id: 'ADD_ADDRESS',
              layout: {
                type: 'SingleColumnLayout',
                children: [
                  {
                    type: 'Form',
                    name: 'form',
                    children: [
                      {
                        type: 'TextHeading',
                        text: 'Add Address',
                      },
                      {
                        type: 'RadioButtonsGroup',
                        label: 'Select Address Type',
                        required: true,
                        name: 'communicationTypes',
                        'data-source': [
                          {
                            id: 'home',
                            title: 'Home',
                          },
                          {
                            id: 'office',
                            title: 'Office',
                          },
                          {
                            id: 'hotel',
                            title: 'Hotel',
                          },
                        ],
                      },
                      {
                        type: 'TextInput',
                        name: 'receiverName',
                        label: "Receiver's Name",
                        'input-type': 'text',
                        required: true,
                      },
                      {
                        type: 'TextInput',
                        name: 'receiverContact',
                        label: "Receiver's Contact",
                        'input-type': 'text',
                        required: true,
                      },
                      {
                        type: 'TextInput',
                        name: 'houseDetails',
                        label: 'House No / Building',
                        'input-type': 'text',
                        required: true,
                      },
                      {
                        type: 'TextInput',
                        name: 'area',
                        label: 'Area / Locality',
                        'input-type': 'text',
                        required: true,
                      },
                      {
                        type: 'TextInput',
                        name: 'landmark',
                        label: 'Landmark',
                        'input-type': 'text',
                        required: false,
                      },
                      {
                        type: 'Footer',
                        label: 'Complete',
                        'on-click-action': {
                          name: 'complete',
                          payload: {
                            landmark: '${form.landmark}',
                            area: '${form.area}',
                            houseDetails: '${form.houseDetails}',
                            receiverContact: '${form.receiverContact}',
                            receiverName: '${form.receiverName}',
                            communicationTypes: '${form.communicationTypes}',
                          },
                        },
                      },
                    ],
                  },
                ],
              },
              title: 'Add Your Address',
              terminal: true,
              success: true,
              data: {},
            },
          ],
        };
        const jsonString = JSON.stringify(addAddressForm);

        formData.append(
          'file',
          new Blob([jsonString], { type: 'application/json' }),
          'flow.json',
        );

        formData.append('name', 'flow.json');
        formData.append('asset_type', 'FLOW_JSON');

        const config = {
          headers: {
            Authorization: `Bearer ${shopAccessToken}`,
          },
        };

        await axios.post(
          `https://graph.facebook.com/v18.0/${flowId}/assets`,
          formData,
          config,
        );
        // Publish a flow
        const flowPublist = await axios.post(
          `https://graph.facebook.com/v18.0/${flowId}/publish`,
          formData,
          config,
        );
        return flowPublist?.data;
      }
    } catch (err) {
      return err.response.data; // Assuming you want to return the error response data
    }
  }

  async changeAudienceName(shopId: string, phone: string, name: string) {
    try {
      const sql = `
     UPDATE public."AudienceShopMapping" cm
      SET audience_name = $2
    FROM public."Audiences" a
    WHERE cm.fk_audience_id = a.audience_id 
      AND cm.fk_shop_id = $1
      AND a.phone_number = $3;
     `;
      await this.databaseService.query(sql, [shopId, name, phone]);

      return {
        message: 'Audience name updated Sucessfully.',
      };
    } catch (error) {
      throw new BadRequestException(error?.response?.data?.error);
    }
  }

  async deleteAudienceInAudienceType(
    shopId: string,
    audienceId: string,
    audienceType: string,
  ) {
    try {
      if (audienceType.includes('SUBTAG$$')) {
        const sql = `
        DELETE FROM public."CustomerTagsMapping" 
        WHERE fk_shop_id = $1
        AND fk_sub_tag_id = $2
        AND  fk_customer_phone =$3;
      `;
        await this.databaseService.query(sql, [
          shopId,
          audienceType.split('SUBTAG$$')[1],
          audienceId,
        ]);
        return {
          message: 'Audience Deleted Sucessfully.',
        };
      }
      const sql = `
      DELETE FROM
        public."AudienceShopMapping" 
      WHERE
         fk_shop_id = $1
        AND  fk_audience_id = $2
        AND  fk_audience_type_id = $3;
    

    `;
      const sql2 = `
           DELETE FROM 
        public."AudienceTypeMapping" 
      WHERE
         fk_audience_id = $1
        AND  fk_audience_type_id = $2;
  
    `;

      // Use parameterized query to safely pass the values
      const params = [shopId, audienceId, audienceType];
      await this.databaseService.query(sql, params);
      await this.databaseService.query(sql2, [audienceId, audienceType]);

      //   const CheckExistence = `
      // SELECT * FROM
      //       public."AudienceTypeMapping" atm
      //   WHERE CAST(atm.fk_audience_id AS text) IN ('${audienceId}')
      //   AND  CAST( atm.fk_audience_type_id AS text) IN  ('${audienceType}');`;
      //   const exists = await this.databaseService.query(CheckExistence);
      //   if (exists.length === 0) {
      //     const sqlQuery = `
      //   DELETE FROM public."Audiences" a
      //   WHERE CAST(audience_id AS text) IN ('${audienceId}') ;

      //    DELETE FROM public."AudienceShopMapping"
      //   WHERE CAST(fk_audience_id AS text) IN ('${audienceId}')
      //      AND fk_shop_id = '${shopId}';

      // `;
      //   }
      // await this.databaseService.query(sqlQuery);

      return {
        message: 'Audience Deleted Sucessfully.',
      };
    } catch (error) {
      throw new BadRequestException(error?.response?.data?.error);
    }
  }

  async getBroadcastHistory(templateId, shopId) {
    try {
      const sql = `
        WITH TypeStats AS (
          SELECT 
            bmd.broadcast_msg_id,
            COUNT(*) as total,
            COUNT(CASE WHEN bma.status = 'Pending' THEN 1 END) as pending,
            COUNT(CASE WHEN bma.delivered_time IS NOT NULL THEN 1 END) as delivered,
            COUNT(CASE WHEN bma.read_time IS NOT NULL THEN 1 END) as read,
            COUNT(CASE WHEN bma.sent_time IS NOT NULL THEN 1 END) as sent,
            COUNT(CASE WHEN bma.failed_time IS NOT NULL THEN 1 END) as failed
          FROM 
            public."BroadcastMessagesData" bmd
          LEFT JOIN 
            public."BroadcastMessageAudience" bma ON bmd.broadcast_msg_id = bma.fk_broadcast_msg_id
          WHERE 
            bmd.fk_shop_id = $1::text
            AND bmd.template_name = $2
          GROUP BY 
            bmd.broadcast_msg_id
        )
        SELECT 
          bmd.template_name,
          bmd.template_lang,
          bmd.broadcast_msg_id,
          bmd.created_at,
          bmd.fk_shop_id,
          pending as pendingmessages,
          total as totalcount,
          COALESCE(ts.delivered, 0) as delivered,
          COALESCE(ts.read, 0) as read,
          COALESCE(ts.sent, 0) as sent,
          COALESCE(ts.failed, 0) as failed
        FROM 
          public."BroadcastMessagesData" bmd
        LEFT JOIN 
          TypeStats ts ON ts.broadcast_msg_id = bmd.broadcast_msg_id
        WHERE 
          bmd.fk_shop_id = $1::text
          AND bmd.template_name = $2
        ORDER BY 
          bmd.created_at DESC;
      `;

      const result = await this.databaseService.query(sql, [
        shopId,
        templateId,
      ]);

      const formattedData = result.map((row, index) => {
        return {
          campaignId: 'Campaign ' + (result.length - index),
          templateName: row.template_name,
          templateLang: row.template_lang,
          broadcastMsgId: row.broadcast_msg_id,
          createdAt: row.created_at,
          pendingMessages: row.pendingmessages,
          totalCount: row.totalcount,
          deliveredCount: row.delivered,
          readCount: row.read,
          sentCount: row.sent,
          failedCount: row.failed,
        };
      });

      return formattedData;
    } catch (error) {
      throw new BadRequestException(
        error?.response?.data?.error || error.message,
      );
    }
  }

  async getPhoneNumbersByBroadcastMsgId(broadcastMsgId: string) {
    try {
      const sql = `
       SELECT 
          bma.phone_number AS "phoneNumber",
          bma.selected_type_name AS "selectedTypeName",
          bma.sent_time AT TIME ZONE 'UTC' AS "messageSentTime",
          bma.delivered_time AT TIME ZONE 'UTC' AS "messageDeliveredTime",
          bma.read_time AT TIME ZONE 'UTC' AS "messageReadTime",
          bma.failed_time AT TIME ZONE 'UTC' AS "messageFailedTime",
          bma.status AS "isMessageSent",
          CASE WHEN bma.delivered_time IS NOT NULL THEN true ELSE false END AS "isMessageDelivered",
          CASE WHEN bma.read_time IS NOT NULL THEN true ELSE false END AS "isMessageRead",
          CASE WHEN bma.failed_time IS NOT NULL THEN true ELSE false END AS "isMessageFailed",
          COALESCE(c.customer_name, a.name) AS "name"
        FROM 
          public."BroadcastMessageAudience" bma
        LEFT JOIN public."Customers" c ON c.phone_number = bma.phone_number AND bma.user_type = 'Customers'
        LEFT JOIN public."Audiences" a ON a.phone_number = bma.phone_number AND bma.user_type = 'Audiences'
        WHERE 
          bma.fk_broadcast_msg_id = $1;

      `;

      const result = await this.databaseService.query(sql, [broadcastMsgId]);

      const formattedData = result.map((row) => {
        return {
          name: row.name || '',
          phoneNumber: row.phoneNumber,
          audiences: row.selectedTypeName || [],
          isMessageSent: row.isMessageSent === 'Sent',
          messageSentTime: row.messageSentTime || '',
          isMessageDelivered: row.isMessageDelivered,
          messageDeliveredTime: row.messageDeliveredTime || '',
          isMessageRead: row.isMessageRead,
          messageReadTime: row.messageReadTime || '',
          isMessageFailed: row.isMessageFailed,
          messageFailedTime: row.messageFailedTime || '',
        };
      });

      return formattedData;
    } catch (error) {
      throw new BadRequestException(
        error?.response?.data?.error || error.message,
      );
    }
  }
  async moveAudience(
    shopId: string,
    fromAudienceId: string,
    toAudienceId: string,
    audienceIds: string[],
  ) {
    const moveQuery = `
      -- Move members from 'fromAudience' to 'toAudience'
      WITH moved_members AS (
        SELECT audience_id, phone_number, name
        FROM public."Audiences"
        WHERE audience_id IN ($1)
      )
      INSERT INTO public."AudienceTypeMapping"(audience_mapping_id, fk_audience_type_id, fk_audience_id)
      SELECT gen_random_uuid(), $2, mm.audience_id
      FROM moved_members mm
      WHERE NOT EXISTS(
        SELECT 1 FROM public."AudienceTypeMapping" atm
        WHERE atm.fk_audience_id = mm.audience_id 
        AND atm.fk_audience_type_id = $2
      );
    `;

    const deleteQuery = `
      -- Remove members from 'fromAudience' type mapping
      DELETE FROM public."AudienceTypeMapping"
      WHERE fk_audience_type_id = $1 AND fk_audience_id IN ($2);
    `;

    try {
      // First, move audience members
      await this.databaseService.query(moveQuery, [
        audienceIds.join("','"),
        toAudienceId,
      ]);

      // Then, delete audience members from the 'fromAudience' mapping
      await this.databaseService.query(deleteQuery, [
        fromAudienceId,
        audienceIds.join("','"),
      ]);

      return { message: 'Audience members moved successfully.' };
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  async copyAudience(
    shopId: string,
    fromAudienceId: string,
    toAudienceId: string,
    audienceIds: string[] = [],
    branchId?: string,
  ) {
    // Ensure audienceIds is not empty
    if (!Array.isArray(audienceIds) || audienceIds.length === 0) {
      throw new BadRequestException(
        'Audience IDs must be provided and be a valid array.',
      );
    }

    // Validate branchId - can be undefined/null but if provided should be a valid string
    const validatedBranchId = branchId || null;

    // Case 1: When toAudienceId includes SUBTAG - copying to a tag
    if (!fromAudienceId.includes('SUBTAG') && toAudienceId.includes('SUBTAG')) {
      try {
        const tagId = toAudienceId.split('$$')[1];

        if (fromAudienceId.includes('open-cart-customers')) {
          const sqlQuery = `
            INSERT INTO public."CustomerTagsMapping"(
              mapping_id, fk_shop_id, fk_sub_tag_id, fk_customer_phone, added_on, fk_branch_id
            )
            SELECT 
              gen_random_uuid(), 
              $1, 
              $2::uuid, 
              c.phone_number, 
              NOW(),
              $4
            FROM public."Customers" c
            JOIN public."Customer_Shops" cc ON c.customer_id = cc.fk_customer_id
            JOIN public."CustomerAnalytics" ca 
              ON ca.fk_customer_id = c.customer_id
              AND ca.fk_shop_id = cc.fk_shop_id
            WHERE 
              cc.fk_shop_id = $1
              AND cc.broadcast = true
              AND ca.type = 'OPEN_CART'
              AND c.phone_number = ANY($3::varchar[])
              AND NOT EXISTS (
                SELECT 1 FROM public."CustomerTagsMapping" ctm
                WHERE ctm.fk_sub_tag_id = $2::uuid
                AND ctm.fk_customer_phone = c.phone_number
                AND ctm.fk_shop_id = $1
                AND (ctm.fk_branch_id = $4::varchar OR ($4 IS NULL AND ctm.fk_branch_id IS NULL))
              );
          `;
          const params = [shopId, tagId, audienceIds, validatedBranchId];
          await this.databaseService.query(sqlQuery, params);
          return { message: 'Open cart customers copied successfully to tag.' };
        }
        if (
          fromAudienceId.includes('active-customers') ||
          fromAudienceId.includes('potential-customers')
        ) {
          const sqlQuery = `
            INSERT INTO public."CustomerTagsMapping"(
              mapping_id, fk_shop_id, fk_sub_tag_id, fk_customer_phone, added_on, fk_branch_id
            )
            SELECT 
              gen_random_uuid(), 
              $1::varchar, 
              $2::uuid, 
              c.phone_number, 
              NOW(),
              $4
            FROM public."Customers" c
            JOIN public."Customer_Shops" cc ON c.customer_id = cc.fk_customer_id
            WHERE 
              cc.fk_shop_id = $1::varchar
              AND cc.broadcast = true
              AND c.phone_number = ANY($3::varchar[])
              AND NOT EXISTS (
                SELECT 1 FROM public."CustomerTagsMapping" ctm
                WHERE ctm.fk_sub_tag_id = $2::uuid
                AND ctm.fk_customer_phone = c.phone_number
                AND ctm.fk_shop_id = $1::varchar
                AND (ctm.fk_branch_id = $4::varchar OR ($4 IS NULL AND ctm.fk_branch_id IS NULL))
              );
          `;
          const params = [shopId, tagId, audienceIds, validatedBranchId];
          await this.databaseService.query(sqlQuery, params);
          return {
            message: fromAudienceId.includes('active-customers')
              ? 'Active customers copied successfully to tag.'
              : 'Potential customers copied successfully to tag.',
          };
        }

        const sqlQuery = `
          INSERT INTO public."CustomerTagsMapping"(
            mapping_id, fk_shop_id, fk_sub_tag_id, fk_customer_phone, added_on, fk_branch_id
          )
          SELECT gen_random_uuid(), $1, $3::uuid, a.phone_number, NOW(), $4
          FROM public."Audiences" a 
          WHERE a.audience_id = ANY($2::uuid[]) AND NOT EXISTS (
            SELECT 1
            FROM public."CustomerTagsMapping"
            WHERE fk_sub_tag_id = $3::uuid
            AND fk_customer_phone = a.phone_number
            AND fk_shop_id = $1::varchar 
            AND (fk_branch_id = $4::varchar OR ($4 IS NULL AND fk_branch_id IS NULL))
          );
        `;
        const params = [shopId, audienceIds, tagId, validatedBranchId];
        await this.databaseService.query(sqlQuery, params);
        return { message: 'Audience members copied successfully to tag.' };
      } catch (error) {
        throw new BadRequestException(error.message);
      }
    }
    // Case 2: When both fromAudienceId and toAudienceId include SUBTAG
    else if (
      fromAudienceId.includes('SUBTAG') &&
      toAudienceId.includes('SUBTAG')
    ) {
      try {
        const fromTagId = fromAudienceId.split('$$')[1];
        const toTagId = toAudienceId.split('$$')[1];

        const sqlQuery = `
          INSERT INTO public."CustomerTagsMapping"(
            mapping_id, fk_shop_id, fk_sub_tag_id, fk_customer_phone, added_on, fk_branch_id)
          SELECT gen_random_uuid(), $1, $2, ctm.fk_customer_phone, NOW(), $5
          FROM public."CustomerTagsMapping" ctm
          WHERE ctm.fk_sub_tag_id = $3
          AND ctm.fk_shop_id = $1::varchar
          AND ctm.fk_customer_phone = ANY($4::varchar[])
          AND NOT EXISTS (
            SELECT 1
            FROM public."CustomerTagsMapping"
            WHERE fk_sub_tag_id = $2
            AND fk_customer_phone = ctm.fk_customer_phone
            AND fk_shop_id = $1::varchar
            AND (fk_branch_id = $5::varchar OR ($5 IS NULL AND fk_branch_id IS NULL))
          );
        `;
        const params = [
          shopId,
          toTagId,
          fromTagId,
          audienceIds,
          validatedBranchId,
        ];
        await this.databaseService.query(sqlQuery, params);

        return {
          message: 'Audience members copied successfully between tags.',
        };
      } catch (error) {
        throw new BadRequestException(error.message);
      }
    }
    // Case 3: When fromAudienceId includes SUBTAG/open-cart-customers but toAudienceId doesn't
    else if (
      (fromAudienceId.includes('SUBTAG') ||
        fromAudienceId === 'open-cart-customers') &&
      !toAudienceId.includes('SUBTAG')
    ) {
      try {
        // Create audiences for new phone numbers
        let createAudiencesQuery = '';
        const params: any = [audienceIds];
        if (fromAudienceId === 'open-cart-customers') {
          createAudiencesQuery = `
            INSERT INTO public."Audiences" (audience_id, phone_number, name)
            SELECT gen_random_uuid(), c.phone_number, COALESCE(cc.customer_name, c.customer_name)
            FROM public."Customers" c
            JOIN public."Customer_Shops" cc ON c.customer_id = cc.fk_customer_id
            JOIN public."CustomerAnalytics" ca 
              ON ca.fk_customer_id = c.customer_id
              AND ca.fk_shop_id = cc.fk_shop_id
            WHERE c.phone_number = ANY($1)
            AND cc.fk_shop_id = $2
            AND cc.broadcast = true
            AND ca.type = 'OPEN_CART'
            AND NOT EXISTS (
              SELECT 1 FROM public."Audiences" a WHERE a.phone_number = c.phone_number
            )
            RETURNING audience_id, phone_number, name;
          `;
          params.push(shopId);
        } else {
          createAudiencesQuery = `
            INSERT INTO public."Audiences" (audience_id, phone_number, name)
            SELECT gen_random_uuid(), c.phone_number, c.customer_name
            FROM public."Customers" c
            WHERE c.phone_number = ANY($1)
            AND NOT EXISTS (
              SELECT 1 FROM public."Audiences" a WHERE a.phone_number = c.phone_number
            )
            RETURNING audience_id, phone_number, name;
          `;
        }

        await this.databaseService.query(createAudiencesQuery, params, false);

        // Map audiences to audience type
        const audienceTypeMappingQuery = `
          INSERT INTO public."AudienceTypeMapping" (audience_mapping_id, fk_audience_type_id, fk_audience_id)
          SELECT gen_random_uuid(), $2, a.audience_id
          FROM public."Audiences" a
          WHERE a.phone_number = ANY($1)
          AND NOT EXISTS (
            SELECT 1 FROM public."AudienceTypeMapping" atm
            WHERE atm.fk_audience_id = a.audience_id 
            AND atm.fk_audience_type_id = $2
          );
        `;

        const params2 = [audienceIds, toAudienceId];
        await this.databaseService.query(
          audienceTypeMappingQuery,
          params2,
          false,
        );

        // Map audiences to shop
        const audienceShopMappingQuery = `
          INSERT INTO public."AudienceShopMapping" (
            audience_shop_id, fk_shop_id, fk_audience_id, broadcast, audience_name, fk_branch_id
          )
          SELECT gen_random_uuid(), $2, a.audience_id, true, a.name, $3
          FROM public."Audiences" a
          WHERE a.phone_number = ANY($1)
          AND NOT EXISTS (
            SELECT 1
            FROM public."AudienceShopMapping" arm
            WHERE arm.fk_audience_id = a.audience_id
            AND arm.fk_shop_id = $2::varchar
            AND (arm.fk_branch_id = $3::varchar OR ($3 IS NULL AND arm.fk_branch_id IS NULL))
          );
        `;

        const shopParams = [audienceIds, shopId, branchId];
        await this.databaseService.query(
          audienceShopMappingQuery,
          shopParams,
          false,
        );

        return {
          message:
            fromAudienceId === 'open-cart-customers'
              ? 'Open cart customers copied to audience group successfully.'
              : 'Audience members copied from tag to audience group.',
        };
      } catch (error) {
        throw new BadRequestException(error.message);
      }
    }
    // Case 4: Default case - neither is a SUBTAG
    else {
      try {
        const copyQuery = `
          INSERT INTO public."AudienceTypeMapping"(audience_mapping_id, fk_audience_type_id, fk_audience_id)
          SELECT gen_random_uuid(), $2, a.audience_id
          FROM public."Audiences" a
          WHERE a.audience_id = ANY($1::uuid[])
          AND NOT EXISTS(
            SELECT 1 FROM public."AudienceTypeMapping" atm
            WHERE atm.fk_audience_id = a.audience_id 
            AND atm.fk_audience_type_id = $2
          );
        `;
        await this.databaseService.query(
          copyQuery,
          [audienceIds, toAudienceId],
          false,
        );
        return { message: 'Audience members copied successfully.' };
      } catch (error) {
        throw new BadRequestException(error.message);
      }
    }
  }

  async getAudiencesByTypes(
    shopId: string,
    audiencePayload: { selectedTypes: string[]; type: 'or' | 'and' },
  ) {
    const { selectedTypes, type } = audiencePayload;

    const tagTypes = selectedTypes.filter((type) => type.includes('SUBTAG$$'));

    // Parse special types that may have prefixes like "Cravin-Dubai$$open-cart-customers"
    const specialTypes = selectedTypes.filter(
      (type) =>
        type.includes('active-customers') ||
        type.includes('potential-customers') ||
        type.includes('open-cart-customers'),
    );

    // Regular types are UUID-based audience types (exclude tags and special types)
    const regularTypes = selectedTypes.filter(
      (type) =>
        !type.includes('SUBTAG$$') &&
        !type.includes('active-customers') &&
        !type.includes('potential-customers') &&
        !type.includes('open-cart-customers'),
    );

    // Format for SQL query
    const tagIds = tagTypes.map((t) => t.split('SUBTAG$$')[1]);
    const tagCondition =
      tagIds.length > 0
        ? `ctm.fk_sub_tag_id IN (${tagIds.map((id) => `'${id}'`).join(',')})`
        : 'false';

    const regularCondition =
      regularTypes.length > 0
        ? `at2.audience_type_id IN (${regularTypes.map((id) => `'${id}'`).join(',')})`
        : 'false';

    // Helper function to check if a special type is included
    const hasSpecialType = (typeToCheck: string) => {
      return specialTypes.some((type) => type.includes(typeToCheck));
    };

    let sqlQuery = `
      WITH RECURSIVE tag_audience AS (
        SELECT DISTINCT
          c.phone_number,
          COALESCE(cc.customer_name, c.customer_name) as name,
          CONCAT('Tag:', mst.tag_name) as audience_type
        FROM public."CustomerTagsMapping" ctm
        JOIN public."Customers" c ON c.phone_number = ctm.fk_customer_phone
        LEFT JOIN public."Customer_Shops" cc ON c.customer_id = cc.fk_customer_id AND cc.fk_shop_id = '${shopId}'
        JOIN public."MasterSubTags" mst ON mst.tag_id = ctm.fk_sub_tag_id
        WHERE ${tagCondition}
        AND ctm.fk_shop_id = '${shopId}'
      ),
      regular_audience AS (
        SELECT DISTINCT
          a.phone_number,
          COALESCE(acm.audience_name, a.name) as name,
          at2.name as audience_type
        FROM public."Audiences" a
        JOIN public."AudienceTypeMapping" atm ON a.audience_id = atm.fk_audience_id
        JOIN public."AudienceTypes" at2 ON at2.audience_type_id = atm.fk_audience_type_id
        JOIN public."AudienceShopMapping" acm ON acm.fk_audience_id = a.audience_id
        WHERE ${regularCondition}
        AND at2.fk_shop_id = '${shopId}'
      ),
      active_customers AS (
        SELECT DISTINCT
          c.phone_number,
          COALESCE(cc.customer_name, c.customer_name) as name,
          'Active Customers' as audience_type
        FROM public."Customers" c
        JOIN public."Customer_Shops" cc ON c.customer_id = cc.fk_customer_id
        JOIN public."ShopOrders" o ON c.customer_id = o.fk_customer_id
        WHERE cc.fk_shop_id = '${shopId}'
        AND cc.broadcast = true
        AND o.fk_shop_id = '${shopId}'
        AND ${hasSpecialType('active-customers')}
      ),
      potential_customers AS (
        SELECT DISTINCT
          c.phone_number,
          COALESCE(cc.customer_name, c.customer_name) as name,
          'Potential Customers' as audience_type
        FROM public."Customers" c
        JOIN public."Customer_Shops" cc ON c.customer_id = cc.fk_customer_id
        WHERE cc.fk_shop_id = '${shopId}'
        AND cc.broadcast = true
        AND NOT EXISTS (
          SELECT 1
          FROM public."ShopOrders" o
          WHERE o.fk_customer_id = c.customer_id
          AND o.fk_shop_id = '${shopId}'
        )
        AND ${hasSpecialType('potential-customers')}
      ),
      open_cart_customers AS (
        SELECT DISTINCT
          c.phone_number,
          COALESCE(cc.customer_name, c.customer_name) as name,
          'Open Cart Customers' as audience_type
        FROM public."Customers" c
        JOIN public."Customer_Shops" cc ON c.customer_id = cc.fk_customer_id
        JOIN public."CustomerAnalytics" ca 
          ON ca.fk_customer_id = c.customer_id
          AND ca.fk_shop_id = cc.fk_shop_id
        WHERE cc.fk_shop_id = '${shopId}'
        AND cc.broadcast = true
        AND ca.type = 'OPEN_CART'
        AND ${hasSpecialType('open-cart-customers')}
      ),
      all_audiences AS (
        SELECT phone_number, name, audience_type 
        FROM tag_audience
        ${tagIds.length > 0 ? '' : 'WHERE false'}
        UNION ALL
        SELECT phone_number, name, audience_type 
        FROM regular_audience
        ${regularTypes.length > 0 ? '' : 'WHERE false'}
        ${
          hasSpecialType('active-customers')
            ? `
        UNION ALL
        SELECT phone_number, name, audience_type 
        FROM active_customers`
            : ''
        }
        ${
          hasSpecialType('potential-customers')
            ? `
        UNION ALL
        SELECT phone_number, name, audience_type 
        FROM potential_customers`
            : ''
        }
        ${
          hasSpecialType('open-cart-customers')
            ? `
        UNION ALL
        SELECT phone_number, name, audience_type 
        FROM open_cart_customers`
            : ''
        }
      ),
      grouped_audiences AS (
        SELECT 
          phone_number,
          MIN(name) as name,
          array_agg(DISTINCT audience_type) as audience_types,
          COUNT(DISTINCT audience_type) as type_count
        FROM all_audiences tm
          WHERE NOT EXISTS(
          SELECT 1 FROM public."Customers" c
            JOIN public."Customer_Shops" cc ON c.customer_id = cc.fk_customer_id
            WHERE c.phone_number = tm.phone_number
              AND cc.fk_shop_id = '${shopId}'
              AND cc.broadcast <> true
              AND c.phone_number IS NOT NULL
          )
        GROUP BY phone_number
        HAVING COUNT(*) > 0
      )`;

    if (type === 'or') {
      sqlQuery += `
        SELECT 
          phone_number,
          name,
          audience_types
        FROM grouped_audiences
        ORDER BY audience_types;
      `;
    } else if (type === 'and') {
      sqlQuery += `
        SELECT 
          phone_number,
          name,
          audience_types
        FROM grouped_audiences
        WHERE type_count >= ${selectedTypes.length}
        ORDER BY audience_types;
      `;
    }

    try {
      const result = await this.databaseService.query(sqlQuery);
      return result || [];
    } catch (error) {
      throw new BadRequestException(
        'Failed to get audiences: ' + (error.message || error),
      );
    }
  }

  async getAudiencesCountByTypes(
    shopId: string,
    audiencePayload: { selectedTypes: string[] },
  ) {
    const { selectedTypes } = audiencePayload;

    const tagTypes = selectedTypes.filter((type) => type.includes('SUBTAG$$'));

    // Parse special types that may have prefixes like "Cravin-Dubai$$open-cart-customers"
    const specialTypes = selectedTypes.filter(
      (type) =>
        type.includes('active-customers') ||
        type.includes('potential-customers') ||
        type.includes('open-cart-customers'),
    );

    // Regular types are UUID-based audience types (exclude tags and special types)
    const regularTypes = selectedTypes.filter(
      (type) =>
        !type.includes('SUBTAG$$') &&
        !type.includes('active-customers') &&
        !type.includes('potential-customers') &&
        !type.includes('open-cart-customers'),
    );

    // Extract tag IDs and format for SQL query
    const tagIds = tagTypes.map((t) => t.split('SUBTAG$$')[1]);
    const tagIdsStr = tagIds.length
      ? tagIds.map((id) => `'${id}'`).join(',')
      : "'00000000-0000-0000-0000-000000000000'"; // Dummy UUID to prevent empty IN clause

    // Format regular types for SQL query - handle empty case correctly to avoid UUID casting errors
    const regularTypesStr =
      regularTypes.length > 0
        ? regularTypes.map((id) => `'${id}'`).join(',')
        : "'00000000-0000-0000-0000-000000000000'"; // Dummy UUID to prevent empty IN clause

    const sqlQuery = `
      WITH RECURSIVE base_audiences AS (
        -- Tag audiences
        SELECT DISTINCT c.phone_number, 1 as source_count
        FROM public."CustomerTagsMapping" ctm
        JOIN public."Customers" c ON c.phone_number = ctm.fk_customer_phone
        WHERE ctm.fk_sub_tag_id IN (${tagIdsStr})
        AND ctm.fk_shop_id = '${shopId}'
        AND ${tagIds.length > 0 ? 'true' : 'false'}

        UNION ALL

        -- Regular audiences
        SELECT DISTINCT a.phone_number, 1
        FROM public."Audiences" a
        JOIN public."AudienceTypeMapping" atm ON a.audience_id = atm.fk_audience_id
        JOIN public."AudienceTypes" at2 ON at2.audience_type_id = atm.fk_audience_type_id
        JOIN public."AudienceShopMapping" acm ON acm.fk_audience_id = a.audience_id
        WHERE at2.audience_type_id IN (${regularTypesStr})
        AND acm.fk_shop_id = '${shopId}'
        AND ${regularTypes.length > 0 ? 'true' : 'false'}

        UNION ALL

        -- Active customers
        SELECT DISTINCT c.phone_number, 1
        FROM public."Customers" c
        JOIN public."Customer_Shops" cc ON c.customer_id = cc.fk_customer_id
        JOIN public."ShopOrders" o ON c.customer_id = o.fk_customer_id
        WHERE cc.fk_shop_id = '${shopId}'
        AND cc.broadcast = true
        AND o.fk_shop_id = '${shopId}'
        AND ${specialTypes.includes('active-customers') ? 'true' : 'false'}

        UNION ALL

        -- Potential customers
        SELECT DISTINCT c.phone_number, 1
        FROM public."Customers" c
        JOIN public."Customer_Shops" cc ON c.customer_id = cc.fk_customer_id
        WHERE cc.fk_shop_id = '${shopId}'
        AND cc.broadcast = true
        AND NOT EXISTS (
          SELECT 1
          FROM public."ShopOrders" o
          WHERE o.fk_customer_id = c.customer_id
          AND o.fk_shop_id = '${shopId}'
        )
        AND ${specialTypes.includes('potential-customers') ? 'true' : 'false'}

        UNION ALL

        -- Open Cart customers
        SELECT DISTINCT c.phone_number, 1
        FROM public."Customers" c
        JOIN public."Customer_Shops" cc ON c.customer_id = cc.fk_customer_id
        JOIN public."CustomerAnalytics" ca 
          ON ca.fk_customer_id = c.customer_id
          AND ca.fk_shop_id = cc.fk_shop_id
        WHERE cc.fk_shop_id = '${shopId}'
        AND cc.broadcast = true
        AND ca.type = 'OPEN_CART'
        AND ${specialTypes.includes('open-cart-customers') ? 'true' : 'false'}
      ),
      aggregated_counts AS (
        SELECT
          phone_number,
          COUNT(*) as type_count
        FROM base_audiences
        GROUP BY phone_number
      )
      SELECT
        (SELECT COUNT(DISTINCT phone_number) FROM base_audiences) as "or",
        (SELECT COUNT(*) FROM aggregated_counts WHERE type_count >= ${selectedTypes.length}) as "and"
      ;`;

    try {
      const result = await this.databaseService.query(sqlQuery);
      return result[0];
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  async storeBroadcast(
    shopId: string,
    payload: {
      templateDetails: {
        templateName: string;
        templateLanguage: string;
        templateType: string;
        imageOrDocURL?: string;
        headerParameters?: string;
        bodyParameters?: string;
      };
      audiencesSelected: string[];
      selectionType: 'AND' | 'OR';
      excludedContacts?: string[];
      scheduleType?: string;
      scheduledDateTime?: string;
    },
  ) {
    // This method uses parameterized SQL queries with positional parameters ($1, $2, etc.)
    // to prevent SQL injection attacks. All user inputs are passed as parameters
    // rather than being concatenated into SQL strings.
    try {
      // Convert the selectionType to lowercase for compatibility with getAudiencesByTypes
      const type = payload.selectionType === 'AND' ? 'and' : 'or';

      // Get audiences based on the selected types
      const audiences = await this.getAudiencesByTypes(shopId, {
        selectedTypes: payload.audiencesSelected,
        type: type,
      });
      if (!audiences || audiences.length === 0) {
        throw new BadRequestException(
          'No audiences found for the selected types',
        );
      }

      const audiencesSelectedTypeName = {};

      for (const audience of audiences) {
        audiencesSelectedTypeName[audience.phone_number] =
          audience.audience_types;
      }

      // Convert audiences to the format expected by the broadcast method
      let phoneNumbers = audiences.map((audience) => audience.phone_number);

      // Filter out excluded contacts if any are provided
      if (payload.excludedContacts && payload.excludedContacts.length > 0) {
        // Create a set of excluded contacts for faster lookups
        const excludedSet = new Set(payload.excludedContacts);

        // Filter out any phone numbers that are in the excluded set
        phoneNumbers = phoneNumbers.filter((phone) => !excludedSet.has(phone));

        if (phoneNumbers.length === 0) {
          throw new BadRequestException(
            'All audiences have been excluded. No recipients available for broadcast.',
          );
        }
      }

      // Extract template details
      const {
        templateName,
        templateLanguage,
        templateType,
        imageOrDocURL = '',
        headerParameters = '',
        bodyParameters = '',
      } = payload.templateDetails;

      // Create temporary tables with parameterized queries
      const setupQuery = `
        DROP TABLE IF EXISTS temp_uuid2;
        DROP TABLE IF EXISTS temp_audience_broadcast;
        CREATE TEMP TABLE temp_uuid2 AS
        SELECT gen_random_uuid() AS broadcast_msg_id;
        
        CREATE TEMP TABLE temp_audience_broadcast (
          fk_broadcast_msg_id UUID,
          status VARCHAR(255),
          phone_number VARCHAR(30),
          user_type VARCHAR(255),
          selected_type_name VARCHAR(255)
        );
      `;

      await this.databaseService.query(setupQuery);

      // Insert into BroadcastMessagesData with parameters
      let insertBroadcastQuery = '';
      let insertBroadcastParams = [];

      if (payload.scheduleType === 'scheduled' && payload.scheduledDateTime) {
        // If schedule is enabled, store the datetime in scheduled_on column
        insertBroadcastQuery = `
          INSERT INTO public."BroadcastMessagesData"(
            fk_shop_id, broadcast_msg_id, template_name, template_lang, template_type, 
            image_doc_url, header_parameters, body_parameters, scheduled_on
          )
          SELECT 
            $1, 
            broadcast_msg_id, 
            $2, 
            $3, 
            $4, 
            $5, 
            $6, 
            $7,
            $8
          FROM temp_uuid2;
        `;
        insertBroadcastParams = [
          shopId,
          templateName,
          templateLanguage,
          templateType,
          imageOrDocURL,
          headerParameters,
          bodyParameters,
          payload.scheduledDateTime,
        ];
      } else {
        // Otherwise, don't include the scheduled_on column
        insertBroadcastQuery = `
          INSERT INTO public."BroadcastMessagesData"(
            fk_shop_id, broadcast_msg_id, template_name, template_lang, template_type, 
            image_doc_url, header_parameters, body_parameters
          )
          SELECT 
            $1, 
            broadcast_msg_id, 
            $2, 
            $3, 
            $4, 
            $5, 
            $6, 
            $7
          FROM temp_uuid2;
        `;
        insertBroadcastParams = [
          shopId,
          templateName,
          templateLanguage,
          templateType,
          imageOrDocURL,
          headerParameters,
          bodyParameters,
        ];
      }

      await this.databaseService.query(
        insertBroadcastQuery,
        insertBroadcastParams,
      );

      // Insert phone numbers into temp table using properly formatted array parameters
      if (phoneNumbers.length > 0) {
        // We'll use a batch insert approach for better performance
        const valueStrings = [];
        const valueParams = [];
        let paramCounter = 1;

        for (const phone of phoneNumbers) {
          const audienceTypesList = audiencesSelectedTypeName[phone] || [
            'Direct Contact',
          ];
          const audienceTypes = JSON.stringify(audienceTypesList);

          // Determine user_type based on the content of audienceTypesList
          let userType = 'Audiences'; // Default

          // Check if any type indicates this is a Customer
          const isCustomer =
            Array.isArray(audienceTypesList) &&
            audienceTypesList.some((type) => {
              if (typeof type === 'string') {
                return (
                  type.includes('Active Customers') ||
                  type.includes('Potential Customers') ||
                  type.includes('Open Cart Customers') ||
                  type.includes('Tag:')
                );
              }
              return false;
            });

          if (isCustomer) {
            userType = 'Customers';
          }

          valueStrings.push(
            `($${paramCounter++}, 'Pending', $${paramCounter++}, $${paramCounter++})`,
          );
          valueParams.push(phone.toString(), userType, audienceTypes);
        }

        if (valueStrings.length > 0) {
          const insertPhoneQuery = `
            INSERT INTO temp_audience_broadcast(
              fk_broadcast_msg_id, phone_number, status, user_type, selected_type_name
            )
            SELECT 
              t.broadcast_msg_id,
              v.phone_number,
              v.status,
              v.user_type,
              v.selected_type_name
            FROM temp_uuid2 t CROSS JOIN (VALUES ${valueStrings.join(', ')}) AS v(phone_number, status, user_type, selected_type_name);
          `;

          await this.databaseService.query(insertPhoneQuery, valueParams);
        }
      }

      // Finalize with deduplication logic using properly formatted parameters
      const finalizeQuery = `
        WITH deduplicated AS (
          SELECT 
            phone_number,
            first_value(fk_broadcast_msg_id) OVER (PARTITION BY phone_number ORDER BY user_type) as fk_broadcast_msg_id,
            'done' as status,
            first_value(user_type) OVER (PARTITION BY phone_number ORDER BY user_type) as user_type,
            selected_type_name
          FROM temp_audience_broadcast tm
          WHERE NOT EXISTS(
            SELECT 1 FROM public."Customers" c
            JOIN public."Customer_Shops" cc ON c.customer_id = cc.fk_customer_id
            WHERE c.phone_number = tm.phone_number
            AND cc.fk_shop_id = $1
            AND cc.broadcast <> true
            AND c.phone_number IS NOT NULL
          )
        )
        INSERT INTO public."BroadcastMessageAudience"(
          fk_broadcast_msg_id, phone_number, status, user_type, selected_type_name
        )
        SELECT 
          fk_broadcast_msg_id,
          phone_number,
          'Pending',
          user_type,
          array_agg(selected_type_name) as selected_type_names
        FROM deduplicated
        GROUP BY fk_broadcast_msg_id, phone_number, status, user_type
        ORDER BY phone_number;
      `;

      // Execute the final query with explicitly casted parameter for better compatibility
      await this.databaseService.query(finalizeQuery, [shopId]);
      return { message: 'Broadcast Queued Successfully.' };
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new BadRequestException(
        'Failed to store broadcast: ' + error.message,
      );
    }
  }

  async getBusinessDetails(shopId: string) {
    try {
      if (!shopId) {
        throw new BadRequestException('Shop ID is required.');
      }

      const sql = `
        SELECT COALESCE(w.current_limit, 250) AS current_limit,  s.shop_business_account_id 
        FROM public."WhatsappBusinessInfo" w
        RIGHT JOIN public."Shops" s ON s.shop_business_account_id = w.whatsapp_business_id
        WHERE s.shop_id = $1;
      `;

      const result = await this.databaseService.query(sql, [shopId]);

      if (!result || result.length === 0) {
        throw new BadRequestException(
          'No business details found for the given shop ID.',
        );
      }

      return result[0];
    } catch (error) {
      console.error('Error fetching business details:', error);
      throw new BadRequestException(error.message);
    }
  }

  normalizeToNull(value: any): any {
    if (
      value === undefined ||
      value === null ||
      value === '' ||
      value === 'null' ||
      value === 'undefined'
    ) {
      return null;
    }
    return value;
  }
}
